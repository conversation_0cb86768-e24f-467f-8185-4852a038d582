program ComprehensiveTest {

  // Function to find maximum of two numbers
  func max(a, b) {
    let result;
    if (a > b) {
      result = a;
    } else {
      result = b;
    }; // End of if-else statement
    return result;
  }

  // Function with no parameters
  func getConstantValue() {
    let val = 100;
    return val;
  }

  // Function demonstrating various expressions and control flow
  func processValues(val1, val2, operationFlag) {
    let processedResult;
    let tempVal;

    tempVal = val1 * val1 + val2 / 2 - 5; // Mixed arithmetic

    if (operationFlag == 1) { // Test ==
      output(1001); // Marker for if branch
      processedResult = max(tempVal, val1 + val2);
    } else {
      output(1002); // Marker for else branch
      if (val1 != val2) { // Test != and nested if
        output(1003); // Marker for nested if
        processedResult = tempVal - (val1 - val2);
      } else {
        output(1004); // Marker for nested else
        processedResult = getConstantValue(); // Function call without args
      }; // End of nested if-else
    }; // End of outer if-else

    return processedResult;
  }

  // Function with a while loop, input, and calling other functions
  func loopAndProcessInput(iterations) {
    let count = 0;
    let sumOfInputs = 0;
    let currentInput;
    let iterationMax;

    output(2000); // Marker: Entering loopAndProcessInput

    while (count < iterations) {
      output(count + 2001); // Output current iteration (offset)
      input(currentInput);  // Read input
      sumOfInputs = sumOfInputs + currentInput;
      
      if (count <= (iterations / 2)) { // Test <=
         output(sumOfInputs);
      }; // End of if

      count = count + 1;
    }; // End of while loop

    iterationMax = processValues(sumOfInputs, iterations, (sumOfInputs >= iterations)); // Test >= and func_call in expr
    output(iterationMax);
    return sumOfInputs;
  }

  main {
    let x = 10;
    let y = 20;
    let z;
    let loopCount = 3;
    let funcResult;
    let mainVarForInput1;
    let mainVarForInput2;

    output(x); // Output initial x
    output(y); // Output initial y

    z = max(x,y); // Function call, assign to z
    output(z);    // Expected: 20

    // Test declare_stmt without assignment, then assign_stmt
    let tempDeclare;
    tempDeclare = -x + (y * 2); // Test unary minus and parens in expr
    output(tempDeclare); // Expected: -10 + 40 = 30

    // Test if_stmt with various conditions
    if (x < y) { // Test <
      output(1); 
    } else {
      output(0);
    }; // End of if-else

    if (z == (x + y)) { // Test complex bool_expr (z should be 20, x+y is 30)
      output(0); // Not equal
    } else {
      output(1); // Equal (Oops, z is 20, x+y is 30, so not equal, this branch is correct)
    }; // End of if-else
    
    // Output multiple expressions
    output(getConstantValue(), x * y, z - x); // Expected: 100, 200, 10

    // Call a more complex function
    funcResult = processValues(x, y, 1); // x=10, y=20, opFlag=1 -> tempVal=100+10-5 = 105. max(105, 30) = 105
    output(funcResult); // Expected: 1001 (marker), 105

    funcResult = processValues(5, 5, 0); // val1=5, val2=5, opFlag=0 -> tempVal=25+2-5 = 22. val1==val2 is true
    output(funcResult); // Expected: 1002 (marker), 1004 (marker), 100 (from getConstantValue)

    // Input statement with multiple idents
    output(3000); // Marker before multi-input
    input(mainVarForInput1, mainVarForInput2); // User provides two numbers
    output(mainVarForInput1);
    output(mainVarForInput2);
    output(mainVarForInput1 + mainVarForInput2);

    // Call function with loop and input
    // For loopCount = 3, user might input e.g., 1, 2, 3
    // sumOfInputs = 1+2+3 = 6
    // iterations = 3
    // processValues(6, 3, (6>=3)=true=1) -> tempVal = 36+1-5=32. max(32, 9)=32
    // loopAndProcessInput returns sumOfInputs = 6
    z = loopAndProcessInput(loopCount); 
    output(z); // Expected: sum of inputs based on user
  }
}