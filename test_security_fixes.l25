// 安全修复测试 - 验证安全漏洞修复
program SecurityTest {
    func test_large_numbers() {
        // 测试大数字处理 (应该正常工作)
        let big = 1000000;
        let bigger = big * 1000;
        output(bigger);
        return bigger;
    }
    
    func test_division_safety() {
        let a = 100;
        let b = 5;
        let c = 0;
        
        // 正常除法
        let result1 = a / b;
        output(result1);  // 应该输出 20
        
        // 这个会在运行时被捕获并报错
        // let result2 = a / c;  // 除零错误
        
        return result1;
    }
    
    func test_array_bounds() {
        let arr[10];
        let i = 0;
        
        // 正常数组访问
        while (i < 10) {
            arr[i] = i * i;
            i = i + 1;
        };

        // 输出数组内容
        i = 0;
        while (i < 10) {
            output(arr[i]);
            i = i + 1;
        };
        
        return arr[5];  // 应该输出 25
    }
    
    func test_deep_recursion(n) {
        let result = 1;
        if (n > 0) {
            result = n + test_deep_recursion(n - 1);
        };
        return result;
    }
    
    main {
        // 测试大数字
        let big_result = test_large_numbers();
        output(big_result);
        
        // 测试除法安全
        let div_result = test_division_safety();
        output(div_result);
        
        // 测试数组边界
        let arr_result = test_array_bounds();
        output(arr_result);
        
        // 测试适度递归 (不会触发限制)
        let rec_result = test_deep_recursion(10);
        output(rec_result);  // 应该输出 1+2+...+10 = 55
        
        // 测试负数处理
        let neg = -42;
        let pos = -neg;
        output(neg);
        output(pos);
    }
}
