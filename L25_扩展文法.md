# L25.c 扩展文法定义

## 扩展后的完整文法

### 程序结构
```
<program> = "program" <ident> "{" { <struct_def> | <func_def> } "main" "{" <stmt_list> "}" "}"
```

### 结构体定义
```
<struct_def> = "struct" <ident> "{" <member_list> "}"
<member_list> = <member_decl> ";" { <member_decl> ";" }
<member_decl> = <ident>
```

### 函数定义
```
<func_def> = "func" <ident> "(" [ <param_list> ] ")" "{" <stmt_list> "return" <expr> ";" "}"
<param_list> = <ident> { "," <ident> }
```

### 语句
```
<stmt_list> = <stmt> ";" { <stmt> ";" }
<stmt> = <declare_stmt> | <assign_stmt> | <if_stmt> | <while_stmt> | <input_stmt> | <output_stmt> | <func_call>

<declare_stmt> = "let" <ident> [ <type_annotation> | <array_decl> ] [ "=" <expr> ]
<type_annotation> = ":" <ident>
<array_decl> = "[" <number> "]"

<assign_stmt> = <ident> [ <array_access> | <struct_access> ] "=" <expr>
<array_access> = "[" <expr> "]"
<struct_access> = "." <ident>

<if_stmt> = "if" "(" <bool_expr> ")" "{" <stmt_list> "}" [ "else" "{" <stmt_list> "}" ]
<while_stmt> = "while" "(" <bool_expr> ")" "{" <stmt_list> "}"
<func_call> = <ident> "(" [ <arg_list> ] ")"
<arg_list> = <expr> { "," <expr> }
<input_stmt> = "input" "(" <ident> { "," <ident> } ")"
<output_stmt> = "output" "(" <expr> { "," <expr> } ")"
```

### 表达式
```
<bool_expr> = <expr> ("==" | "!=" | "<" | "<=" | ">" | ">=") <expr>
<expr> = [ "+" | "-" ] <term> { ("+" | "-") <term> }
<term> = <factor> { ("*" | "/") <factor> }
<factor> = <ident> [ <array_access> | <struct_access> ] | <number> | "(" <expr> ")" | <func_call>
```

### 基本元素
```
<ident> = <letter> { <letter> | <digit> }
<number> = <digit> { <digit> }
<letter> = "a" | "b" | ... | "z" | "A" | "B" | ... | "Z"
<digit> = "0" | "1" | ... | "9"
```

## 扩展功能说明

### 1. 一维静态数组
- **声明语法**: `let arr[10];` - 声明一个包含10个整数的数组
- **访问语法**: `arr[index]` - 访问数组的第index个元素
- **赋值语法**: `arr[index] = value;` - 给数组元素赋值
- **特点**: 
  - 数组大小在声明时确定，不可改变
  - 数组元素类型为整数
  - 数组索引从0开始

### 2. 结构体
- **定义语法**: 
  ```
  struct Point {
      x;
      y;
  }
  ```
- **声明语法**: `let p : Point;` - 声明一个Point类型的结构体变量
- **成员访问**: `p.x` - 访问结构体成员
- **成员赋值**: `p.x = 10;` - 给结构体成员赋值
- **特点**:
  - 结构体成员类型为整数
  - 支持嵌套访问（在表达式中使用）

### 3. 新增的词法元素
- `struct` - 结构体关键字
- `.` - 成员访问操作符
- `:` - 类型注解操作符
- `[` `]` - 数组访问操作符

### 4. 语义规则
1. 结构体必须先定义后使用
2. 数组大小必须是正整数常量
3. 数组和结构体变量必须先声明后使用
4. 结构体成员必须存在于对应的结构体定义中
5. 数组索引必须是整数表达式
6. 每个语句块至少包含一条语句
7. 函数体必须以return语句结束
8. main块和普通语句块中不能使用return

## 示例程序

```l25
program ArrayStructTest {
    struct Point {
        x;
        y;
    }
    
    func distance(p1x, p1y, p2x, p2y) {
        let dx;
        let dy;
        dx = p1x - p2x;
        dy = p1y - p2y;
        return dx * dx + dy * dy;
    }
    
    main {
        let points[3];
        let p : Point;
        let i;
        let dist;
        
        // 初始化结构体
        p.x = 10;
        p.y = 20;
        
        // 初始化数组
        points[0] = 1;
        points[1] = 2;
        points[2] = 3;
        
        // 输出结果
        output(p.x, p.y);
        
        i = 0;
        while (i < 3) {
            output(points[i]);
            i = i + 1;
        }
        
        // 计算距离
        dist = distance(p.x, p.y, points[0], points[1]);
        output(dist);
    }
}
```
