program TestVariablesAndControlFlow {
  func multiply(x, y) {
    let product = x * y;
    return product;
  }

  main {
    let a = 10;
    let b = 20;
    let c;
    c = multiply(a, 5); // c should be 50
    output(c);

    if (c > b) { // 50 > 20 is true
      output(c - b); // Output 30
    } else {
      output(0);
    };

    let result = multiply(a, b); // result = 10 * 20 = 200
    output(result);
  }
}