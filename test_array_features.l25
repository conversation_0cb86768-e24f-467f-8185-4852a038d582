// 数组功能测试 - 验证数组扩展
program ArrayTest {
    func array_sum(arr_size) {
        let arr[10];
        let i = 0;
        let sum = 0;
        
        // 初始化数组
        while (i < arr_size) {
            arr[i] = i * 2;
            i = i + 1;
        }
        
        // 计算数组和
        i = 0;
        while (i < arr_size) {
            sum = sum + arr[i];
            i = i + 1;
        }
        
        return sum;
    }
    
    func bubble_sort() {
        let arr[5];
        let i = 0;
        let j = 0;
        let temp = 0;
        
        // 初始化数组 [5, 2, 8, 1, 9]
        arr[0] = 5;
        arr[1] = 2;
        arr[2] = 8;
        arr[3] = 1;
        arr[4] = 9;
        
        // 冒泡排序
        i = 0;
        while (i < 4) {
            j = 0;
            while (j < 4 - i) {
                if (arr[j] > arr[j + 1]) {
                    temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
                j = j + 1;
            }
            i = i + 1;
        }
        
        // 输出排序结果
        i = 0;
        while (i < 5) {
            output(arr[i]);
            i = i + 1;
        }
        
        return arr[0];
    }
    
    main {
        let result = array_sum(5);
        output(result);  // 应该输出 0+2+4+6+8 = 20
        
        let first = bubble_sort();
        output(first);   // 应该输出 1 (排序后的第一个元素)
        
        // 测试大数组
        let big_arr[100];
        big_arr[0] = 42;
        big_arr[99] = 99;
        output(big_arr[0]);
        output(big_arr[99]);
    }
}
