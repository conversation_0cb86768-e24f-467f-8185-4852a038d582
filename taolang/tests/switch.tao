function main() {
    switch 1 {
    case 1:
        switch 2 {
        case 1,2,3:
            println(__case);
            break;
            println("won't go here");
        default:
            println("2d");
        }
        println("11");
    default:
        println("?");
    }
    {
        let a = 1;
        switch a {
        case +a:
            println("+a");
        case a > 0:
            println("a > 0");
        case 1:
            println("1");
        }
    }
    println("end main");
}
