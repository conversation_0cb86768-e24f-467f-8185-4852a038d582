function main() {
    each();
    map();
    reduce();
    find();
    filter();
    where();
    select();
    groupBy();
}

function each() {
    let a = [1,3,5,7,9];
    a.each(e=>println(e));
}

function map() {
    let a = [1,3,5,7,9];
    let b = a.map(x=>x*x);
    println(b);
}

function reduce() {
    let a = [1,2,3];
    let b = a.reduce((memo,num)=>memo+num,0);
    println(b);
}

function find() {
    let a = [1,3,5,7,9];
    let b = a.find(x=>x>5);
    println(b);
}

function filter() {
    let a = [1,2,3,4,5,6,7,8,9];
    let b = a.filter(x=>x%2==0);
    println(b);
}

function where() {
    let a = [
        {a:1,b:3},
        {a:2,b:2},
        {a:3,b:1},
    ];
    let b = a.where(x=>x.a==1||x.b==1);
    println(b);
}

function select() {
    let a = [
        {name:"<PERSON>",  age:10,},
        {name:"<PERSON>",    age:20,},
        {name:"<PERSON><PERSON><PERSON>", age:30,},
        {name:"<PERSON>",  age:40,},
        {name:"Elle",   age:50,},
    ];

    let s = a.select(x=>x.name);
    println(s);
}

function groupBy() {
    let a = [
        "one", "One", "ONE",
        "two", "Two", "TWO",
        "three", "Three", "THREE",
    ];

    let b = a.groupBy(x=>x.lower());

    b.each(x=>{
        println("group:", x.group);
        println("array:", x);
        println();
    });
}
