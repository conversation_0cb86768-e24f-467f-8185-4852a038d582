html, body {
    margin: auto;
    padding: 0;
    width: 800px;
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

#body {
    display: flex;
}

#body .left {
    flex: 1;
}

#body .right {
    flex: 1;
    margin-left: 1em;
}

#editor {
    border: 1px solid gray;
    border-radius: 4px;
    margin: 1em;
}

#editor textarea {
    padding: .5em;
    box-sizing: border-box;
    display: block;
    width: 100%;
    border: none;
    resize: none;
    font-family: monospace;
    white-space: pre;
}

#source {
    height: 250px;
}
#result {
    height: 125px;
}

#editor .vert-split {
    height: 1px;
    background-color: gray;
}

#toolbar {
    overflow: hidden;
    margin: 1em;
}

#foot {
    text-align: center;
}