program ArrayStructTest {
    struct Point {
        x;
        y;
    }
    
    func add(a, b) {
        return a + b;
    }
    
    main {
        let arr[5];
        let p : Point;
        let i;
        let sum;
        
        // 测试结构体
        p.x = 10;
        p.y = 20;
        output(p.x);
        output(p.y);
        
        // 测试数组
        arr[0] = 1;
        arr[1] = 2;
        arr[2] = 3;
        arr[3] = 4;
        arr[4] = 5;
        
        i = 0;
        sum = 0;
        while (i < 5) {
            sum = add(sum, arr[i]);
            output(arr[i]);
            i = i + 1;
        }
        
        output(sum);
        
        // 混合使用
        p.x = arr[0];
        p.y = arr[4];
        output(p.x);
        output(p.y);
    }
}
