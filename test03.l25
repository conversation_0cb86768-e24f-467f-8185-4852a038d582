program TestWhileAndInput {
  main {
    let counter = 0;
    let sum = 0;
    let limit;

    output(100); // 这是一个标记，表示程序开始

    input(limit);  // 程序将等待用户输入一个数字作为循环的上限

    while (counter < limit) {
      sum = sum + counter;
      counter = counter + 1;
      output(counter); // 在循环内部输出当前的 counter 值
      output(sum);     // 在循环内部输出当前的 sum 值
    }; // 注意：while 语句作为一条语句，在它的右花括号 '}' 之后需要一个分号

    output(200); // 这是另一个标记，表示循环结束
    output(sum);   // 输出最终的 sum 值
    output(counter); // 输出最终的 counter 值
  }
}