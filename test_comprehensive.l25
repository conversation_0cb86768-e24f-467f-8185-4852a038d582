program ComprehensiveTest {
    struct Vector {
        x;
        y;
        z;
    }
    
    struct Matrix {
        a;
        b;
        c;
        d;
    }
    
    func dot_product(v1x, v1y, v2x, v2y) {
        return v1x * v2x + v1y * v2y;
    }
    
    func array_sum(arr_size) {
        let sum;
        let idx;
        let temp_arr[10];

        sum = 0;
        idx = 0;
        while (idx < arr_size) {
            temp_arr[idx] = idx + 1;
            sum = sum + temp_arr[idx];
            idx = idx + 1;
        }
        return sum;
    }
    
    main {
        let numbers[5];
        let v1 : Vector;
        let v2 : Vector;
        let m : Matrix;
        let i;
        let result;
        
        // 初始化数组
        numbers[0] = 10;
        numbers[1] = 20;
        numbers[2] = 30;
        numbers[3] = 40;
        numbers[4] = 50;
        
        // 初始化向量
        v1.x = 3;
        v1.y = 4;
        v1.z = 5;
        
        v2.x = 1;
        v2.y = 2;
        v2.z = 3;
        
        // 初始化矩阵
        m.a = 1;
        m.b = 2;
        m.c = 3;
        m.d = 4;
        
        // 输出数组内容
        output(numbers[0]);
        output(numbers[1]);
        output(numbers[2]);
        output(numbers[3]);
        output(numbers[4]);
        
        // 输出向量内容
        output(v1.x);
        output(v1.y);
        output(v1.z);
        
        // 计算点积
        result = dot_product(v1.x, v1.y, v2.x, v2.y);
        output(result);
        
        // 使用数组函数
        result = array_sum(5);
        output(result);
        
        // 混合操作
        v1.x = numbers[0];
        v2.y = numbers[4];
        m.a = v1.x + v2.y;
        output(m.a);
        
        // 数组和结构体的复杂操作
        i = 0;
        while (i < 3) {
            if (i == 0) {
                numbers[i] = v1.x;
            } else {
                if (i == 1) {
                    numbers[i] = v1.y;
                } else {
                    numbers[i] = v1.z;
                }
            }
            output(numbers[i]);
            i = i + 1;
        }
    }
}
