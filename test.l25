// test_struct.l25
// A complex test for L25 struct implementation.

program StructFeatureTest {

    // Define a struct to represent a 2D point.
    struct Point {
        x;  // The x-coordinate
        y;  // The y-coordinate
    }

    // A utility function to calculate the squared distance from origin.
    // L25 doesn't have sqrt(), so we'll work with squared values.
    func squaredDist(val_x, val_y) {
        let sq_x = val_x * val_x;
        let sq_y = val_y * val_y;
        return sq_x + sq_y;
    }

    main {
        // 1. Declare two instances of the Point struct.
        // Each Point has 2 members, so p1 reserves 2 slots, p2 reserves another 2.
        output(101); // Marker: Start of main
        let p1 : Point;
        let p2 : Point;

        // 2. Assign values to the members of both struct instances.
        p1.x = 3;
        p1.y = -4;

        p2.x = 10;
        p2.y = 5;
        output(102); // Marker: Assignments done

        // 3. Read and output members to verify assignments.
        output(p1.x, p1.y); // Expected: 3, -4
        output(p2.x, p2.y); // Expected: 10, 5

        // 4. Use members in control flow (if statement).
        if (p2.x > p1.x) {
            output(888); // Expected: This should print, as 10 > 3.
        };

        // 5. Use members as arguments to a function call.
        let dist1 = squaredDist(p1.x, p1.y);
        output(dist1); // Expected: 3*3 + (-4)*(-4) = 9 + 16 = 25

        // 6. Use members from different structs in a single expression.
        let delta_y = p2.y - p1.y;
        output(delta_y); // Expected: 5 - (-4) = 9
    }
}