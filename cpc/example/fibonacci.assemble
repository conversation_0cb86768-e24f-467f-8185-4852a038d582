(4301258760)     NVAR 0
(4301258776)     LEA  2
(4301258792)     LI  
(4301258800)     PUSH
(4301258808)     IMM  1
(4301258824)     LE  
(4301258832)     JZ   4301258872
(4301258848)     IMM  1
(4301258864)     RET 
(4301258872)     LEA  2
(4301258888)     LI  
(4301258896)     PUSH
(4301258904)     IMM  1
(4301258920)     SUB 
(4301258928)     PUSH
(4301258936)     CALL 4301258760
(4301258952)     DARG 1
(4301258968)     PUSH
(4301258976)     LEA  2
(4301258992)     LI  
(4301259000)     PUSH
(4301259008)     IMM  2
(4301259024)     SUB 
(4301259032)     PUSH
(4301259040)     CALL 4301258760
(4301259056)     DARG 1
(4301259072)     ADD 
(4301259080)     RET 
(4301259088)     NVAR 1
(4301259104)     LEA  -1
(4301259120)     PUSH
(4301259128)     IMM  0
(4301259144)     SI  
(4301259152)     LEA  -1
(4301259168)     LI  
(4301259176)     PUSH
(4301259184)     IMM  10
(4301259200)     LE  
(4301259208)     JZ   4301259480
(4301259224)     IMM  4302307328
(4301259240)     PUSH
(4301259248)     LEA  -1
(4301259264)     LI  
(4301259272)     PUSH
(4301259280)     LEA  -1
(4301259296)     LI  
(4301259304)     PUSH
(4301259312)     CALL 4301258760
(4301259328)     DARG 1
(4301259344)     PUSH
(4301259352)     PRTF
(4301259360)     DARG 2
(4301259376)     LEA  -1
(4301259392)     PUSH
(4301259400)     LEA  -1
(4301259416)     LI  
(4301259424)     PUSH
(4301259432)     IMM  1
(4301259448)     ADD 
(4301259456)     SI  
(4301259464)     JMP  4301259152
(4301259480)     IMM  0
(4301259496)     RET 
