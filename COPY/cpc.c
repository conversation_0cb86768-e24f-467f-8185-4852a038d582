#include <fcntl.h>  // 文件控制操作，如 open
#include <unistd.h> // POSIX 操作系统 API，如 read, close, write
#include <stdint.h> // 标准整型类型，如 int64_t
#include <stdio.h>  // 标准输入输出，如 printf
#include <stdlib.h> // 标准库函数，如 malloc, free, exit
#include <memory.h> // 内存操作函数，如 memset, memcmp
#include <string.h> // 字符串操作函数，如 strlen

// 将所有 int 类型重定义为 int64_t，以在64位系统上处理更大的数值或地址
#define int int64_t

// 定义虚拟机各段内存的最大尺寸
int MAX_SIZE;

// 虚拟机内存段
int * code,         // 代码段，存放编译后的指令
    * code_dump,    // 代码段的副本，用于调试时输出汇编指令
    * stack;        // 栈段，用于函数调用、局部变量存储等
char* data;         // 数据段，存放字符串字面量和全局/静态变量

// 虚拟机寄存器
int * pc,           // 程序计数器 (Program Counter)，指向下一条要执行的指令
    * sp,           // 栈顶指针 (Stack Pointer)
    * bp;           // 基址指针 (Base Pointer)，指向当前活动记录（栈帧）的底部

int ax,             // 通用累加寄存器 (Accumulator Register)，用于存放指令操作的结果
    cycle;          // 虚拟机执行周期计数，用于调试或性能分析

// 指令集 (Instruction Set)
// 从 c4 编译器复制而来，并将 JSR/ENT/ADJ/LEV/BZ/BNZ 更改为 CALL/NVAR/DARG/RET/JZ/JNZ
enum {
    IMM,  // 将立即数加载到 ax
    LEA,  // 将局部变量地址（相对于 bp）加载到 ax
    JMP,  // 无条件跳转
    JZ,   // 如果 ax 为0，则跳转
    JNZ,  // 如果 ax 不为0，则跳转
    CALL, // 调用函数
    NVAR, // 为局部变量在栈上分配空间 (New VARiables)
    DARG, // 从栈上丢弃参数 (Discard ARGuments)
    RET,  // 从函数返回
    LI,   // 从 ax 指向的地址加载整数到 ax (Load Int)
    LC,   // 从 ax 指向的地址加载字符到 ax (Load Char)
    SI,   // 将 ax 中的整数存儲到栈顶指针指向的地址 (Store Int)
    SC,   // 将 ax 中的字符存储到栈顶指针指向的地址 (Store Char)
    PUSH, // 将 ax 的值压入栈中
    OR,   // 逻辑或
    XOR,  // 逻辑异或
    AND,  // 逻辑与
    EQ,   // 等于
    NE,   // 不等于
    LT,   // 小于
    GT,   // 大于
    LE,   // 小于等于
    GE,   // 大于等于
    SHL,  // 左移
    SHR,  // 右移
    ADD,  // 加法
    SUB,  // 减法
    MUL,  // 乘法
    DIV,  // 除法
    MOD,  // 取模
    OPEN, // 系统调用：打开文件
    READ, // 系统调用：读取文件
    CLOS, // 系统调用：关闭文件
    PRTF, // 系统调用：printf
    MALC, // 系统调用：malloc
    FREE, // 系统调用：free
    MSET, // 系统调用：memset
    MCMP, // 系统调用：memcmp
    EXIT  // 系统调用：退出程序
};

// 词法单元类型 / 关键字
// 不支持 for 循环
enum {
    Num = 128, // 数字字面量 (起始值128是为了避免与ASCII码冲突)
    Fun,      // 函数
    Sys,      // 系统调用
    Glo,      // 全局变量/函数
    Loc,      // 局部变量
    Id,       // 标识符
    Char,     // 'char' 关键字
    Int,      // 'int' 关键字
    Enum,     // 'enum' 关键字
    If,       // 'if' 关键字
    Else,     // 'else' 关键字
    Return,   // 'return' 关键字
    Sizeof,   // 'sizeof' 关键字
    While,    // 'while' 关键字

    // 运算符，按优先级排序 (实际上这里的顺序不完全是严格的优先级顺序，解析时会用其他方法处理)
    Assign, // =
    Cond,   // ?: (条件运算符)
    Lor,    // || (逻辑或)
    Land,   // && (逻辑与)
    Or,     // | (位或)
    Xor,    // ^ (位异或)
    And,    // & (位与 / 取地址)
    Eq,     // == (等于)
    Ne,     // != (不等于)
    Lt,     // < (小于)
    Gt,     // > (大于)
    Le,     // <= (小于等于)
    Ge,     // >= (大于等于)
    Shl,    // << (左移)
    Shr,    // >> (右移)
    Add,    // +
    Sub,    // -
    Mul,    // * (乘 / 解引用)
    Div,    // /
    Mod,    // %
    Inc,    // ++ (自增)
    Dec,    // -- (自减)
    Brak    // [] (数组下标)
};

// 符号表条目字段索引
// 从 c4 复制，并将 HXX (Hidden) 重命名为 GXX (Global backup)
enum {
    Token,    // 词法单元类型 (如 Id, Num, Fun)
    Hash,     // 符号的哈希值，用于快速查找
    Name,     // 指向符号名称字符串的指针
    Class,    // 符号类别 (如 Num, Fun, Sys, Glo, Loc)
    Type,     // 符号的数据类型 (如 CHAR, INT, PTR)
    Value,    // 符号的值 (如变量地址、函数入口地址、枚举值)
    GClass,   // 保存全局符号被局部符号覆盖前的 Class
    GType,    // 保存全局符号被局部符号覆盖前的 Type
    GValue,   // 保存全局符号被局部符号覆盖前的 Value
    SymSize   // 每个符号表条目的大小 (字段数量)
};

// 变量和函数的数据类型
enum {
    CHAR, // char 类型
    INT,  // int 类型
    PTR   // 指针类型 (通过与基础类型相加来表示多级指针，如 INT + PTR 表示 int*)
};

// 源代码相关指针
char* src,      // 指向当前正在解析的源代码字符
    * src_dump; // 指向源代码的起始位置，用于备份和重置

// 符号表相关指针
int * symbol_table, // 指向符号表的基地址
    * symbol_ptr,   // 指向当前处理的符号表条目
    * main_ptr;     // 指向 'main' 函数在符号表中的条目

int token,      // 当前词法单元的类型
    token_val;  // 当前词法单元的值 (如果是数字，则为数值；如果是字符串，则为数据段地址)
int line;       // 当前解析的行号

// 词法分析器 (Tokenizer)
// 从源代码字符串 `src` 中逐个读取字符，识别并返回词法单元 (token)
void tokenize() {
    char* ch_ptr; // 用于记录标识符或字符串的起始位置
    while((token = *src++)) { // 读取一个字符，如果不是文件末尾 (0)
        if (token == '\n') {
            line++; // 行号增加
        }
        // 跳过预处理指令 (以 # 开头)
        else if (token == '#') {
            while (*src != 0 && *src != '\n') {
                src++;
            }
        }
        // 处理标识符 (字母或下划线开头，后跟字母、数字或下划线)
        else if ((token >= 'a' && token <= 'z') || (token >= 'A' && token <= 'Z') || (token == '_')) {
            ch_ptr = src - 1; // 记录标识符起始位置
            // 计算标识符的哈希值，这里用一个简单的乘加哈希
            while ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z')
                    || (*src >= '0' && *src <= '9') || (*src == '_')) {
                token = token * 147 + *src++;
            }
            // 将哈希值与标识符长度结合，形成最终的哈希 (避免不同长度但前缀相同的标识符哈希冲突)
            token = (token << 6) + (src - ch_ptr);
            symbol_ptr = symbol_table; // 从符号表头开始查找
            // 在符号表中查找是否已存在该标识符
            while(symbol_ptr[Token]) { // 如果当前符号表条目有效 (Token 不为0)
                if (token == symbol_ptr[Hash] && !memcmp((char*)symbol_ptr[Name], ch_ptr, src - ch_ptr)) {
                    // 哈希值相同且字符串内容相同，则找到已存在的符号
                    token = symbol_ptr[Token]; // 获取该符号的 Token 类型
                    return;
                }
                symbol_ptr = symbol_ptr + SymSize; // 指向下一个符号表条目
            }
            // 如果未找到，则为新符号
            symbol_ptr[Name] = (int)ch_ptr;   // 存储符号名称的指针 (指向源代码中的实际位置)
            symbol_ptr[Hash] = token;         // 存储计算出的哈希值
            token = symbol_ptr[Token] = Id;   // 将当前 token 类型设为 Id，并存入符号表
            return;
        }
        // 处理数字字面量
        else if (token >= '0' && token <= '9') {
            token_val = token - '0'; // 获取第一个数字
            // 十进制 (1-9 开头)
            if (token_val) {
                while (*src >= '0' && *src <= '9') {
                    token_val = token_val * 10 + *src++ - '0';
                }
            }
            // 十六进制 (0x 或 0X 开头)
            else if (*src == 'x' || *src == 'X') {
                token = *++src; // 跳过 'x' 或 'X'
                token_val = 0;
                while ((token >= '0' && token <= '9') || (token >= 'a' && token <= 'f')
                        || (token >= 'A' && token <= 'F')) {
                    // 将字符转换为对应的十六进制数值
                    token_val = token_val * 16 + (token & 0xF) + (token >= 'A' ? 9 : 0);
                    token = *++src;
                }
            }
            // 八进制 (0 开头，后面是0-7)
            else {
                while (*src >= '0' && *src <= '7') {
                    token_val = token_val * 8 + *src++ - '0';
                }
            }
            token = Num; // 当前 token 类型为数字
            return;
        }
        // 处理字符串字面量 (双引号 ") 和字符字面量 (单引号 ')
        else if (token == '"' || token == '\'') {
            ch_ptr = data; // 字符串/字符将存储在数据段，ch_ptr 指向其在数据段的起始位置
            while (*src != 0 && *src != token) { // 读取直到匹配的引号或文件末尾
                token_val = *src++;
                // 处理转义字符
                if (token_val == '\\') {
                    token_val = *src++;
                    // 只支持 '\n' 转义
                    if (token_val == 'n') {
                        token_val = '\n';
                    }
                }
                // 如果是字符串 (双引号)，则将字符存入数据段
                if (token == '"') {
                    *data++ = token_val;
                }
            }
            src++; // 跳过末尾的引号
            if (token == '"') {
                token_val = (int)ch_ptr; // 字符串的值是它在数据段的地址
                *data++ = 0;             // 字符串末尾添加 '\0'
            }
            // 单个字符字面量被当作 Num 类型处理，其 token_val 就是字符的ASCII码
            else {
                token = Num;
            }
            return;
        }
        // 处理注释 (//) 或除法运算符 (/)
        else if (token == '/') {
            if (*src == '/') { // 如果是 //，则为单行注释
                // 跳过注释内容直到行末或文件末尾
                while (*src != 0 && *src != '\n') {
                    src++;
                }
            } else { // 否则是除法运算符
                token = Div;
                return;
            }
        }
        // 处理各种运算符 (大部分直接从 c4 复制)
        else if (token == '=') {if (*src == '=') {src++; token = Eq;} else token = Assign; return;}
        else if (token == '+') {if (*src == '+') {src++; token = Inc;} else token = Add; return;}
        else if (token == '-') {if (*src == '-') {src++; token = Dec;} else token = Sub; return;}
        else if (token == '!') {if (*src == '=') {src++; token = Ne;} /* else it's logical NOT, handled in parse_expr */ return;}
        else if (token == '<') {if (*src == '=') {src++; token = Le;} else if (*src == '<') {src++; token = Shl;} else token = Lt; return;}
        else if (token == '>') {if (*src == '=') {src++; token = Ge;} else if (*src == '>') {src++; token = Shr;} else token = Gt; return;}
        else if (token == '|') {if (*src == '|') {src++; token = Lor;} else token = Or; return;}
        else if (token == '&') {if (*src == '&') {src++; token = Land;} else token = And; return;}
        else if (token == '^') {token = Xor; return;}
        else if (token == '%') {token = Mod; return;}
        else if (token == '*') {token = Mul; return;}
        else if (token == '[') {token = Brak; return;}
        else if (token == '?') {token = Cond; return;}
        // 单字符的界符，直接返回其ASCII码作为 token 类型
        else if (token == '~' || token == ';' || token == '{' || token == '}' || token == '(' || token == ')' || token == ']' || token == ',' || token == ':') {
            return;
        }
        // 忽略其他所有无法识别的字符 (如空格、制表符等)
    }
    // 文件结束时，token 为 0
}

// 断言函数：检查当前 token 是否为期望的 tk，如果不是则报错退出
// 并自动调用 tokenize() 获取下一个 token
void assert(int tk) {
    if (token != tk) {
        printf("line %lld: expect token: %lld(%c), get: %lld(%c)\n", line, tk, (char)tk, token, (char)token);
        exit(-1);
    }
    tokenize();
}

// 检查当前 token 是否为 Id，并且该 Id 在当前作用域 (函数内) 是否已经声明为局部变量
// 用于防止局部变量重复声明
void check_local_id() {
    if (token != Id) {
        printf("line %lld: invalid identifer\n", line);
        exit(-1);
    }
    // symbol_ptr 是 tokenize 后指向符号表中匹配项的指针
    if (symbol_ptr[Class] == Loc) { // 如果该标识符的 Class 已经是 Loc (局部)
        printf("line %lld: duplicate declaration\n", line);
        exit(-1);
    }
}

// 检查当前 token 是否为 Id，并且该 Id 是否已经在全局或当前局部作用域声明过
// 用于确保新声明的标识符是全新的
void check_new_id() {
    if (token != Id) {
        printf("line %lld: invalid identifer\n", line);
        exit(-1);
    }
    // symbol_ptr 是 tokenize 后指向符号表中匹配项的指针
    // 如果 Class 不为0，说明这个标识符已经在符号表中存在 (可能是关键字、已声明的变量/函数等)
    if (symbol_ptr[Class]) {
        printf("line %lld: duplicate declaration\n", line);
        exit(-1);
    }
}

// 解析 enum 定义
// enum { ident1 [= val1], ident2 [= val2], ... }
void parse_enum() {
    int i = 0; // 默认的枚举值，从0开始自增
    while (token != '}') {
        check_new_id(); // 检查枚举成员名是否为新的标识符
        assert(Id);     // 期望一个标识符
        // 处理自定义枚举值 (ident = value)
        if (token == Assign) {
            assert(Assign); // 期望 '='
            assert(Num);    // 期望一个数字
            i = token_val;  // 使用用户指定的值
        }
        // 将枚举成员存入符号表
        symbol_ptr[Class] = Num; // 枚举成员的类别视为 Num (常量)
        symbol_ptr[Type]  = INT; // 枚举成员的类型视为 INT
        symbol_ptr[Value] = i++; // 存储枚举值，并为下一个枚举成员准备值
        if (token == ',') {
            tokenize(); // 如果是逗号，则获取下一个 token (可能是另一个枚举成员或 '}')
        }
    }
}

// 解析基本数据类型 (char 或 int)
int parse_base_type() {
    if (token == Char) {
        assert(Char);
        return CHAR;
    } else {
        assert(Int); // 如果不是 Char，则必须是 Int
        return INT;
    }
}

// 当进入局部作用域（如函数定义）时，如果局部变量名与全局变量名冲突，
// 则临时保存全局变量的符号表信息，以便函数结束后恢复。
void hide_global() {
    symbol_ptr[GClass] = symbol_ptr[Class];
    symbol_ptr[GType]  = symbol_ptr[Type];
    symbol_ptr[GValue] = symbol_ptr[Value];
}

// 退出局部作用域时，恢复被局部变量覆盖的全局变量的符号表信息。
void recover_global() {
    symbol_ptr[Class] = symbol_ptr[GClass];
    symbol_ptr[Type]  = symbol_ptr[GType];
    symbol_ptr[Value] = symbol_ptr[GValue];
}

int ibp; // 当前函数栈帧中，参数和局部变量相对于基址指针 bp 的偏移量基准。
         // 参数在 bp 正向偏移，局部变量在 bp 负向偏移（通常）。
         // 这里 ibp 似乎用于记录参数个数 + 1，或者局部变量分配的起始偏移。

// 解析函数参数列表
// (type *... ident, type *... ident, ...)
void parse_param() {
    int type; // 参数类型
    int i = 0;  // 参数计数，也用于计算参数在栈帧中的偏移
    while (token != ')') { // 直到遇到 ')'
        type = parse_base_type(); // 解析参数的基本类型
        // 解析指针类型 (如 int *, char **, etc.)
        while (token == Mul) { // Mul 也代表 '*'
            assert(Mul);
            type = type + PTR; // 每多一个 '*'，类型值加 PTR
        }
        check_local_id(); // 检查参数名是否在当前（函数）作用域中已声明为局部变量
                          // (理论上参数本身就是一种局部声明，这里可能是防止同名)
        assert(Id);       // 期望参数名

        hide_global();    // 如果参数名与全局变量同名，隐藏全局变量
        // 将参数信息存入符号表
        symbol_ptr[Class] = Loc;    // 参数类别为 Loc (局部)
        symbol_ptr[Type]  = type;   // 参数类型
        symbol_ptr[Value] = i++;    // 参数的值是它在栈帧中的偏移 (相对于调用者栈帧或特定约定)
                                    // 这里 i 从0开始递增，表示第0, 1, 2...个参数
        if (token == ',') {
            assert(','); // 如果有逗号，则期望下一个参数
        }
    }
    ibp = ++i; // ibp 记录参数数量 + 1。这个值将用于后续局部变量的偏移计算。
               // 例如，如果有2个参数 (i=0, i=1)，则 i 变为 2, ibp = 3。
               // 局部变量的偏移可能从 bp - (ibp) 开始，或者参数从 bp + (ibp - value - 1) 开始。
               // 具体看虚拟机中 CALL 和 NVAR 指令如何处理栈帧。
}

int type; // 全局变量，用于在递归的表达式解析中传递当前子表达式的类型

// 解析表达式 (Expression Parser)
// 使用优先级攀爬法 (Precedence Climbing Method) 解析带优先级的二元运算符
// precd 参数表示当前允许解析的最低运算符优先级
void parse_expr(int precd) {
    int tmp_type;      // 临时存储类型，用于类型转换或指针运算
    int i;             // 临时变量，用于计数或存储 token 类型
    int* tmp_ptr;      // 临时指针，通常用于保存跳转指令的目标地址，待后续回填

    // 处理一元前缀运算符和基本表达式单元 (Primary Expressions)
    // 数字字面量
    if (token == Num) {
        tokenize();          // 获取下一个 token
        *++code = IMM;       // 生成 IMM 指令
        *++code = token_val; // IMM 的操作数是数字的值
        type = INT;          // 数字的类型是 INT
    }
    // 字符串字面量
    else if (token == '"') {
        *++code = IMM;           // 生成 IMM 指令
        *++code = token_val;     // IMM 的操作数是字符串在数据段的地址
        assert('"');             // 期望一个结束的双引号 (tokenize() 已处理了字符串内容)
        // 此版本编译器似乎不支持多行字符串字面量拼接 "abc" "def"
        // while (token == '"') assert('"'); //  c4 中用于处理 "abc" "def" 形式，这里似乎不需要

        // data 指针在 tokenize 时已经指向了字符串 '\0' 之后的位置
        // 将 data 指针向上对齐到8字节边界 (如果需要的话)
        // (int)data + 7 是为了向上取整，& -8 (即 & ~7) 是为了清零低3位，实现8字节对齐
        // 不过这里是 data = (char*)((int)data + 8 & -8); 似乎是 data += 8 然后对齐？
        // 应该是确保字符串后的下一个数据分配从8字节对齐的地址开始。
        // 对于字符串本身，tokenize() 中 *data++ = 0; 已经添加了结束符。
        // 这个对齐可能是为后续全局变量分配或者其他数据段使用。
        data = (char*)(((int)data + 7) & ~7); // 正确的8字节向上对齐

        type = PTR; // 字符串的类型是 PTR (指向 CHAR 的指针)
    }
    // sizeof 运算符
    else if (token == Sizeof) {
        tokenize();     // 吃掉 sizeof
        assert('(');    // 期望 '('
        type = parse_base_type(); // 解析 sizeof 的操作数类型 (char 或 int)
        while (token == Mul) { // 处理指针类型 sizeof(int*), sizeof(char**)
            assert(Mul);
            type = type + PTR;
        }
        assert(')');    // 期望 ')'
        *++code = IMM;  // 生成 IMM 指令
        // sizeof(char) 为 1, sizeof(int) 或 sizeof(pointer) 为 8 (因为 int 被 define 为 int64_t)
        *++code = (type == CHAR) ? 1 : 8;
        type = INT;     // sizeof 表达式的结果类型是 INT
    }
    // 标识符 (变量、函数调用、枚举成员)
    else if (token == Id) {
        tokenize();        // 吃掉 Id, symbol_ptr 指向其在符号表中的条目
        tmp_ptr = symbol_ptr; // 保存当前符号表指针，因为递归调用 parse_expr 可能改变 symbol_ptr
        // 函数调用
        if (token == '(') {
            assert('('); // 期望 '('
            i = 0;       // 参数计数器
            // 解析参数列表
            while (token != ')') {
                parse_expr(Assign); // 递归解析每个参数表达式 (Assign 是最低的二元运算符优先级之一)
                *++code = PUSH;     // 将参数表达式的结果压栈
                i++;
                if (token == ',') {
                    assert(',');
                }
            }
            assert(')'); // 期望 ')'

            // 根据符号表中记录的函数类别生成不同的调用指令
            if (tmp_ptr[Class] == Sys) { // 系统调用
                *++code = tmp_ptr[Value]; // Value 字段存储的是系统调用的指令码 (如 OPEN, PRTF)
            } else if (tmp_ptr[Class] == Fun) { // 普通函数调用
                *++code = CALL;            // 生成 CALL 指令
                *++code = tmp_ptr[Value];  // CALL 的操作数是函数的入口地址
            } else {
                printf("line %lld: invalid function call\n", line);
                exit(-1);
            }
            // 如果有参数，生成 DARG 指令清理栈上的参数
            if (i > 0) {
                *++code = DARG;
                *++code = i;   // DARG 的操作数是参数个数
            }
            type = tmp_ptr[Type]; // 函数调用的结果类型是函数声明的返回类型
        }
        // 枚举成员 (其 Class 为 Num)
        else if (tmp_ptr[Class] == Num) {
            *++code = IMM;
            *++code = tmp_ptr[Value]; // Value 字段存储枚举成员的值
            type = INT;               // 枚举成员的类型是 INT
        }
        // 变量
        else {
            if (tmp_ptr[Class] == Loc) { // 局部变量
                *++code = LEA;           // 生成 LEA 指令，加载局部变量地址到 ax
                // Value 字段存储的是局部变量相对于 bp 的偏移 (由 parse_param 或 parse_fun 中的局部变量声明设置)
                // ibp 在 parse_param 后是参数数量 + 1
                // 对于参数: 偏移是 bp + (ibp - 1 - param_index_from_0) * 8 (假设参数在高地址)
                // 对于局部变量: 偏移是 bp - (var_index_from_1) * 8 (假设局部变量在低地址)
                // 这里的 tmp_ptr[Value] 对于参数是 0, 1, ...
                // 对于局部变量是在 parse_fun 中设置的 ++i (i 从 ibp 开始)
                // LEA 操作数是相对于 bp 的偏移 *字数* (不是字节数)
                // 这里用 ibp - tmp_ptr[Value] 需要仔细对应虚拟机栈帧布局
                // 假设栈帧：[ret_addr] [old_bp] [loc_var_n] ... [loc_var_1] <-- bp
                //            [param_1] ... [param_m] <-- bp + (m+1)*word_size + word_size
                // 那么局部变量地址是 bp - value (value是第几个局部变量，从1开始)
                // 参数地址是 bp + (ibp - value) (value是第几个参数，从0开始, ibp是参数个数)
                // 需要确认此处的 LEA 操作数和虚拟机如何解释它
                // 从c4的习惯来看，LEA 操作数应该是 bp + offset，这里 `ibp - tmp_ptr[Value]`
                // 如果 tmp_ptr[Value] 是参数索引(0 to N-1), ibp 是 N. 那么 LEA bp + (N - param_idx).
                // 如果是局部变量索引(N to M-1), 那么 LEA bp + (N - local_idx). (N是参数个数，M是总局部单元数)
                // 这里的 `ibp - tmp_ptr[Value]` 似乎是统一的，
                // 对于参数(Value=0..k-1, ibp=k+1): LEA k+1-Value -> bp+(k+1), ..., bp+2
                // 对于局部变量(Value=k+1..m, ibp=k+1): LEA k+1-Value -> bp+0, bp-1, ...
                // 这与通常的 x86 栈帧参数在 bp 之上，局部变量在 bp 之下有点不同，或者说 LEA 的操作数解释不同。
                // 通常：参数 bp + (idx+2)*wordsize， 局部变量 bp - (idx+1)*wordsize
                // 结合 run_vm 中 `else if (op == LEA) ax = (int)(bp + *pc++);`
                // LEA 的操作数就是直接加到 bp 上的 word 偏移。
                // 假设 parse_param 中 symbol_ptr[Value] = i++ (i from 0), ibp = num_args.
                //   参数0: value=0. (ibp - 0) -> ibp.
                //   参数N-1: value=N-1. (ibp - (N-1)) -> 1.
                //   所以参数的偏移是 bp+1, bp+2, ..., bp+ibp
                // 假设 parse_fun 中局部变量 symbol_ptr[Value] = ++i (i from ibp (num_args))
                //   局部变量0: value=ibp. (ibp - ibp) -> 0.
                //   局部变量M-1: value=ibp+M-1. (ibp - (ibp+M-1)) -> -(M-1).
                //   所以局部变量的偏移是 bp+0, bp-1, ..., bp-(M-1)
                // 这个栈帧布局是: [ret] [old_bp] <--bp [local0] [local1] ... [param_N-1] ... [param0]
                // 即 bp 指向 old_bp, 局部变量在 bp 之下 (负偏移), 参数在 bp 之上 (正偏移).
                // LEA 的操作数 `ibp - tmp_ptr[Value]` 是正确的。
                *++code = ibp - tmp_ptr[Value];
            } else if (tmp_ptr[Class] == Glo) { // 全局变量
                *++code = IMM;           // 生成 IMM 指令，加载全局变量地址到 ax
                *++code = tmp_ptr[Value]; // Value 字段存储的是全局变量在数据段的地址
            } else {
                printf("line %lld: invalid variable\n", line);
                exit(-1);
            }
            type = tmp_ptr[Type]; // 变量的类型
            // 根据类型生成加载指令 (LC 或 LI)
            // 此时 ax 中是变量的地址，需要从该地址加载值到 ax
            *++code = (type == CHAR) ? LC : LI;
        }
    }
    // 类型转换 (cast) 或括号表达式
    else if (token == '(') {
        assert('(');
        // 类型转换: (type) expr
        if (token == Char || token == Int) {
            // tokenize(); // 这里不应该 tokenize，因为 Char 或 Int 本身就是 token
            tmp_type = (token == Char) ? CHAR : INT; // 获取基础类型
            tokenize(); // 吃掉 Char 或 Int
            while (token == Mul) { // 处理指针类型转换 (int*), (char**)
                assert(Mul);
                tmp_type = tmp_type + PTR;
            }
            assert(')');
            // Inc 是所有一元运算符中优先级较高的一个 (用于 parse_expr 的 precd 参数)
            parse_expr(Inc); // 递归解析被转换的表达式
            type = tmp_type; // 表达式的最终类型是转换后的类型
        }
        // 括号表达式: (expr)
        else {
            parse_expr(Assign); // 递归解析括号内的表达式 (Assign 是最低优先级)
            assert(')');
        }
    }
    // 解引用运算符: *expr
    else if (token == Mul) {
        tokenize();      // 吃掉 '*'
        parse_expr(Inc); // 解析被解引用的表达式 (通常是个指针)
        if (type >= PTR) { // 检查类型是否为指针
            type = type - PTR; // 解引用后，类型降低一级指针 (如 int* -> int)
        } else {
            printf("line %lld: invalid dereference\n", line);
            exit(-1);
        }
        // ax 中是被解引用表达式的值 (即地址)，需要从该地址加载数据
        *++code = (type == CHAR) ? LC : LI; // 根据解引用后的基类型生成加载指令
    }
    // 取地址运算符: &expr
    else if (token == And) { // '&'
        tokenize();      // 吃掉 '&'
        parse_expr(Inc); // 解析被取地址的表达式 (通常是个左值)
        // 如果被取地址的表达式最后生成的是 LC 或 LI (加载值的指令)，
        // 那么我们不需要值，而是需要它的地址。ax 在 LC/LI 执行前就已经是地址了。
        // 所以回退一条指令，取消 LC 或 LI。
        if (*code == LC || *code == LI) {
            code--;
        } else {
            // 如果不是 LC/LI，说明表达式不是一个简单的变量加载，不能取地址
            // (例如 & (x+y) 是非法的, &(123) 也是非法的)
            printf("line %lld: invalid reference\n", line);
            exit(-1);
        }
        type = type + PTR; // 取地址后，类型增加一级指针 (如 int -> int*)
    }
    // 逻辑非运算符: !expr
    else if (token == '!') {
        tokenize();      // 吃掉 '!'
        parse_expr(Inc); // 解析表达式
        // 实现 !expr: PUSH, IMM 0, EQ (expr == 0)
        *++code = PUSH;
        *++code = IMM;
        *++code = 0;
        *++code = EQ;
        type = INT;      // 逻辑运算结果为 INT (0 或 1)
    }
    // 按位取反运算符: ~expr
    else if (token == '~') {
        tokenize();      // 吃掉 '~'
        parse_expr(Inc); // 解析表达式
        // 实现 ~expr: PUSH, IMM -1, XOR (expr ^ 0xFF..FF)
        *++code = PUSH;
        *++code = IMM;
        *++code = -1; // -1 在二进制补码表示中是全1
        *++code = XOR;
        type = INT;      // 位运算结果为 INT
    }
    // 正号运算符: +expr (实际上不起作用，但语法允许)
    // 注意：这里 `token == And` 应该是 `token == Add` 且后面没有 `++`
    // 但词法分析器中 `+` 如果后面不是 `+` 则直接返回 `Add`
    // 此处代码与 c4 一致，`+expr` 被视为 `Add` token 后直接解析 `expr`
    // 实际 c4 中一元 `+` 被忽略，但这里可能是笔误，应该检查 token 是 `Add`
    // 假设这里是处理一元正号
    /* else if (token == Add) { // 正确的应该是 Add
        tokenize(); parse_expr(Inc); type = INT;
    } */
    // 负号运算符: -expr
    else if (token == Sub) { // '-'
        tokenize();      // 吃掉 '-'
        parse_expr(Inc); // 解析表达式
        // 实现 -expr: PUSH, IMM 0, SUB (0 - expr) 或者 PUSH, IMM -1, MUL (expr * -1)
        // 这里用的是 expr * -1
        *++code = PUSH;
        *++code = IMM;
        *++code = 0; // c4 用的是 0, SUB
        *++code = IMM; // 当前版本用 -1, MUL
        *++code = -1;
        *++code = MUL;
        type = INT;
    }
    // 前缀自增/自减: ++var, --var
    else if (token == Inc || token == Dec) {
        i = token;       // 保存是 Inc 还是 Dec
        tokenize();      // 吃掉 '++' 或 '--'
        parse_expr(Inc); // 解析变量 (应该是左值)
        // 此时 ax 中是 var 的值，但栈顶(通过回退 code 指针得到)是 var 的地址
        // var 的地址是在解析 Id 时通过 LEA 或 IMM 生成，然后通过 LC/LI 加载值到 ax
        // *code 现在指向 LC 或 LI 指令
        // 我们需要先保存 var 的地址，然后加载 var 的值，进行运算，再写回
        // 假设 parse_expr(Inc) 产生的代码序列是 ..., ADDR_OP, ADDR, LOAD_OP
        // e.g. for local var `a`: ..., LEA, offset, LI
        // code 指针在 LI 之后。 *(code-2) 是 LEA, *(code-1) 是 offset, *code 是 LI
        // 为了实现 ++var:
        // 1. 执行 ADDR_OP, ADDR (获取地址到 ax)
        // 2. PUSH (保存地址)
        // 3. LOAD_OP (加载值到 ax)
        // 4. PUSH (保存原值)
        // 5. IMM 1/8, ADD/SUB (ax = 原值 +/- 1或8)
        // 6. SC/SI (将新值写回之前 PUSH 的地址)
        // 7. ax 中需要是新值
        // 当前代码的实现:
        // parse_expr(Inc) 已经把变量的值加载到 ax，并且 *code 是 LC/LI
        // 1. 将 LC/LI 改为 PUSH (此时栈顶是变量地址，ax 是变量值。这是错的！)
        //    应该是 parse_expr 先不加载值，只产生地址到ax。
        //    或者，如果 parse_expr 已经加载了值到ax，那么地址信息已经用掉了。
        //    c4 的实现是: `if (*code == LC) { *code = PUSH; *++code = LC; } ...`
        //    这意味着先 PUSH 地址，再 LC 加载值。
        //    当前代码：
        //    `if (*code == LC) {*code = PUSH; *++code = LC;}`
        //    `else if (*code == LI) {*code = PUSH; *++code = LI;}`
        //    这会在加载指令(LC/LI)前插入一个PUSH。
        //    执行到这里时，ax 中是变量的地址。
        //    1. PUSH (ax, 即变量地址压栈)
        //    2. LC/LI (从ax指向的地址加载值到ax, ax=var_value)
        //    接下来：
        //    `*++code = PUSH;` // PUSH (ax, 即var_value压栈)
        //    `*++code = IMM; *++code = (type > PTR) ? 8 : 1;` // IMM, step
        //    `*++code = (i == Inc) ? ADD : SUB;` // ax = var_value +/- step
        //    `*++code = (type == CHAR) ? SC : SI;` // *(stack_top = var_addr) = ax (新值写回)
        //    此时 ax 是新值。符合 ++var 的语义 (表达式结果是新值)
        if (*code == LC) { *code = PUSH; *++code = LC; } // PUSH addr, then load val
        else if (*code == LI) { *code = PUSH; *++code = LI; }
        else {
            printf("line %lld: invalid Inc or Dec (must be lvalue)\n", line);
            exit(-1);
        }
        *++code = PUSH; // PUSH original value of var
        *++code = IMM;
        *++code = (type > PTR) ? 8 : 1; // step for pointer or basic type
        *++code = (i == Inc) ? ADD : SUB; // ax = val + step
        // ax now holds the new value. Store it back.
        // The address of var is on stack below the original value.
        // SC/SI expects address on stack and value in ax.
        *++code = (type == CHAR) ? SC : SI; // *(var_addr) = new_value
        // ax still holds new_value, which is correct for prefix ++/--.
    }
    // 未知的一元表达式或 primary expression
    else {
        printf("line %lld: invalid expression (token: %lld '%c')\n", line, token, (char)token);
        exit(-1);
    }

    // 处理二元运算符 (Precedence Climbing)
    // 当遇到的运算符 token 的优先级 >= 当前 precd 时，继续解析
    while (token >= precd) {
        tmp_type = type; // 保存左操作数的类型
        // 赋值运算符: =
        if (token == Assign) {
            tokenize(); // 吃掉 '='
            // 左操作数必须是左值，其地址在执行 LC/LI 前已在 ax 中。
            // LC/LI 会把值加载到 ax。对于赋值，我们需要地址。
            // 所以如果上一条指令是 LC 或 LI (加载值的指令)，将其改为 PUSH。
            // 这会将变量的地址压栈。
            // 例如 `a = b;` `a` 解析后，代码可能是 LEA offset_a, LI。
            // 改为 LEA offset_a, PUSH。栈顶是 addr_a。ax 还是 addr_a。
            if (*code == LC || *code == LI) {
                *code = PUSH; // ax (address of LHS) is pushed onto stack
            } else {
                // 如果不是 LC/LI，说明左边不是一个简单的变量，不能赋值
                printf("line %lld: invalid assignment (LHS not an lvalue)\n", line);
                exit(-1);
            }
            parse_expr(Assign); // 递归解析右操作数 (Assign 优先级最低，右结合)
                                // 右操作数的值在 ax 中
            // type 是右操作数的类型, tmp_type 是左操作数的类型。
            // 赋值时，值的类型应遵循左操作数的类型。
            type = tmp_type;
            // 生成存储指令 (SC 或 SI)
            // SC/SI: *(address_from_stack) = value_in_ax
            *++code = (type == CHAR) ? SC : SI;
        }
        // 条件运算符 (三元运算符): expr1 ? expr2 : expr3
        else if (token == Cond) { // '?'
            tokenize();      // 吃掉 '?'
            *++code = JZ;    // 如果 expr1 (结果在 ax) 为 0，则跳转到 expr3 部分
            tmp_ptr = ++code; // 保存 JZ 指令的操作数地址，待回填 (跳转到 expr3 的地址)

            parse_expr(Assign); // 解析 expr2 (true 分支)
            assert(':');     // 期望 ':'

            // JZ 的目标是 expr3 的起始位置。expr2 解析完后，code+3 的位置是 JMP 指令之后，即 expr3 开始的地方。
            // (JZ target_false, expr2_code, JMP end_if, expr3_code, end_if_label)
            // *tmp_ptr (JZ target) should point to start of expr3_code
            // current code points to end of expr2_code.
            // We need a JMP over expr3 if expr1 was true.
            // No, if expr1 is true, JZ doesn't jump. We execute expr2. Then we need to JMP over expr3.
            // Correct sequence:
            // eval expr1
            // JZ to_false_branch (to expr3)
            // eval expr2 (true_branch)
            // JMP to_after_cond_expr
            // to_false_branch:
            // eval expr3
            // to_after_cond_expr:
            *tmp_ptr = (int)(code + 3); // JZ 的目标是 expr3 的代码块，即 JMP 指令(2 words)之后
                                        // (current code) -> JMP -> placeholder -> (start of expr3)

            *++code = JMP;          // 生成 JMP 指令，跳过 expr3 (如果 expr1 为真)
            tmp_ptr = ++code;       // 保存 JMP 指令的操作数地址，待回填 (跳转到条件表达式结束后的地址)

            parse_expr(Cond); // 解析 expr3 (false 分支)
                              // Cond 优先级比 Assign 高，所以用 Cond
            *tmp_ptr = (int)(code + 1); // JMP 的目标是 expr3 结束后的下一条指令
            // 结果类型是 expr2 和 expr3 中更“宽”的类型，这里简单设为 expr3 的类型，或者应该有类型兼容性检查。
            // c4 中没有显式处理类型，这里 type 会被 expr3 的类型覆盖。
        }
        // 逻辑或: || (左结合)
        else if (token == Lor) {
            tokenize();      // 吃掉 '||'
            // 如果左操作数 (ax) 非0，则整个表达式为真 (1)，短路跳转到结果处理
            *++code = JNZ;   // Jump if Not Zero (ax != 0)
            tmp_ptr = ++code;// 保存 JNZ 操作数地址，目标是表达式结果为1的代码
            // 如果左操作数为0，则继续解析右操作数
            parse_expr(Land); // Land 比 Lor 优先级高
            // JNZ 的目标是右操作数计算完毕后的下一条指令，此时 ax 中是右操作数的值，
            // 也就是整个表达式的值。
            // 如果 JNZ 发生，ax 是左值 (非0)。如果没发生，ax 是右值。
            // c4 的实现：如果 JNZ, ax=1。否则 ax=eval(RHS)。
            // 这里：JNZ skips RHS. If LHS is true, ax is already non-zero.
            // If LHS is false, JNZ doesn't happen, ax becomes result of RHS.
            // This is fine. But to make result 0/1:
            // if (LHS) { ax = 1 } else { ax = (RHS != 0) }
            // c4: JNZ to_set_ax_true; eval RHS; JZ to_set_ax_false; to_set_ax_true: IMM 1; JMP end; to_set_ax_false: IMM 0; end:
            // 当前实现：ax = LHS; JNZ end; ax = RHS; end: (ax is 0 or 1 only if operands are)
            // A simpler way for Lor: if LHS is true, result is 1. Else, result is (RHS is true).
            // LHS_val in ax.
            // JNZ set_true (ax is already non-zero, could be 1, or any non-zero)
            // parse_expr(Land) -> RHS_val in ax
            // JMP end
            // set_true: IMM 1 (ax=1)
            // end:
            // *tmp_ptr = (int)(code + 1); // JNZ jumps to the instruction after RHS evaluation
            // The current code simply lets ax be the value of the non-zero operand or the RHS.
            // For logical ops, we often want a 0 or 1.
            // A common way:
            //  eval L
            //  JNZ L_true
            //  eval R
            //  JNZ L_true  (or MOV R to ax)
            //  IMM 0
            //  JMP end
            // L_true:
            //  IMM 1
            // end:
            // Simpler in cpc:
            // eval L (in ax)
            // JNZ skip_R (ax is L's value, non-zero)
            // eval R (in ax) -> ax is R's value
            // skip_R:
            //  (ax is now either L's non-zero value, or R's value if L was zero)
            //  To make it 0 or 1: ax = (ax != 0) -> PUSH, IMM 0, NE
            *tmp_ptr = (int)(code + 1); // If LHS is non-zero, JNZ jumps over RHS evaluation. ax holds LHS.
                                      // Otherwise, RHS is evaluated and ax holds RHS.
                                      // This is not standard 0/1. It's short-circuit value.
            type = INT;
        }
        // 逻辑与: && (左结合)
        else if (token == Land) {
            tokenize();      // 吃掉 '&&'
            // 如果左操作数 (ax) 为0，则整个表达式为假 (0)，短路跳转
            *++code = JZ;
            tmp_ptr = ++code; // 保存 JZ 操作数地址
            parse_expr(Or);   // Or 比 Land 优先级低 (c4: parse_expr(Xor))
                              // 应该是 Or, Xor, And, Eq, Ne, Lt... 的顺序，这里是Or
            *tmp_ptr = (int)(code + 1); // If LHS is zero, JZ jumps over RHS. ax holds LHS (0).
                                      // Otherwise, RHS is evaluated and ax holds RHS.
            type = INT;
        }
        // 位或: |
        else if (token == Or)  {tokenize(); *++code = PUSH; parse_expr(Xor); *++code = OR;  type = INT;}
        // 位异或: ^
        else if (token == Xor) {tokenize(); *++code = PUSH; parse_expr(And); *++code = XOR; type = INT;}
        // 位与: &
        else if (token == And) {tokenize(); *++code = PUSH; parse_expr(Eq);  *++code = AND; type = INT;}
        // 等于: ==
        else if (token == Eq)  {tokenize(); *++code = PUSH; parse_expr(Lt);  *++code = EQ;  type = INT;} // Lt/Gt/Le/Ge are relational, higher than Eq/Ne
        // 不等于: !=
        else if (token == Ne)  {tokenize(); *++code = PUSH; parse_expr(Lt);  *++code = NE;  type = INT;}
        // 小于: <
        else if (token == Lt)  {tokenize(); *++code = PUSH; parse_expr(Shl); *++code = LT;  type = INT;} // Shl/Shr are shift, higher than relational
        // 大于: >
        else if (token == Gt)  {tokenize(); *++code = PUSH; parse_expr(Shl); *++code = GT;  type = INT;}
        // 小于等于: <=
        else if (token == Le)  {tokenize(); *++code = PUSH; parse_expr(Shl); *++code = LE;  type = INT;}
        // 大于等于: >=
        else if (token == Ge)  {tokenize(); *++code = PUSH; parse_expr(Shl); *++code = GE;  type = INT;}
        // 左移: <<
        else if (token == Shl) {tokenize(); *++code = PUSH; parse_expr(Add); *++code = SHL; type = INT;} // Add/Sub are additive, higher than shift
        // 右移: >>
        else if (token == Shr) {tokenize(); *++code = PUSH; parse_expr(Add); *++code = SHR; type = INT;}
        // 加法: +
        else if (token == Add) {
            tokenize();
            *++code = PUSH;   // PUSH 左操作数的值 (tmp_type 是其类型)
            parse_expr(Mul);  // 解析右操作数 (Mul/Div/Mod 优先级更高)
                              // type 是右操作数的类型
            // 指针加法: ptr + int  or  int + ptr
            // tmp_type 是 LHS 类型, type 是 RHS 类型
            // 如果 LHS 是指针 (tmp_type > PTR) 且 RHS 是整数 (type == INT): ptr + int
            if (tmp_type > PTR && type == INT) { // ptr + int
                *++code = PUSH; // PUSH a copy of RHS (int value)
                *++code = IMM;
                *++code = 8;    // sizeof(*ptr) is 8 for int* or char** (scaled by element size)
                *++code = MUL;  // ax = int_val * sizeof(*ptr)
            }
            // 如果 RHS 是指针 (type > PTR) 且 LHS 是整数 (tmp_type == INT): int + ptr
            // This case needs the int (LHS, already PUSHed) to be scaled.
            // The PUSHed LHS is at sp. ax has RHS (ptr).
            // To do: tmp = *sp; *sp = ax; ax = tmp; (swap ax and stack top)
            // Then scale ax (which is LHS_int), then ADD.
            // cpc doesn't seem to handle `int + ptr` as elegantly.
            // It assumes ptr is always LHS if scaling is needed.
            // The current code only scales if LHS (tmp_type) is PTR.
            // If tmp_type is PTR and type is INT (ptr + int):
            //   ax = RHS (int)
            //   PUSH (ax)
            //   IMM 8
            //   MUL (ax = scaled_RHS)
            //   ADD (ax = PUSHed_LHS_ptr + ax) -> correct
            *++code = ADD;
            // 结果类型：如果是 ptr + int，结果是 ptr 类型；否则是 int。
            if (tmp_type > PTR && type == INT) type = tmp_type; // ptr + int -> ptr
            else if (type > PTR && tmp_type == INT) type = type; // int + ptr -> ptr
            else type = INT; // int + int -> int
        }
        // 减法: -
        else if (token == Sub) {
            tokenize();
            *++code = PUSH;   // PUSH 左操作数的值
            parse_expr(Mul);  // 解析右操作数
            // 指针减法:
            // 1. ptr - int: scale int, then subtract. Result is ptr. (tmp_type > PTR, type == INT)
            // 2. ptr1 - ptr2: subtract, then divide by sizeof_element. Result is int. (tmp_type > PTR, type == tmp_type)
            if (tmp_type > PTR && type == INT) { // ptr - int
                *++code = PUSH; // PUSH int value (RHS)
                *++code = IMM;
                *++code = 8;
                *++code = MUL;  // ax = scaled_RHS_int
                *++code = SUB;  // ax = LHS_ptr - scaled_RHS_int
                type = tmp_type; // Result is ptr
            }
            else if (tmp_type > PTR && tmp_type == type) { // ptr1 - ptr2 (same type of pointers)
                *++code = SUB;  // ax = ptr1_val - ptr2_val (byte difference)
                *++code = PUSH; // PUSH byte difference
                *++code = IMM;
                *++code = 8;    // sizeof_element
                *++code = DIV;  // ax = (ptr1 - ptr2) / sizeof_element
                type = INT;     // Result is int
            }
            else { // int - int
                *++code = SUB;
                type = INT;
            }
        }
        // 乘法: *
        else if (token == Mul) {tokenize(); *++code = PUSH; parse_expr(Inc); *++code = MUL; type = INT;} // Inc for unary ops
        // 除法: /
        else if (token == Div) {tokenize(); *++code = PUSH; parse_expr(Inc); *++code = DIV; type = INT;}
        // 取模: %
        else if (token == Mod) {tokenize(); *++code = PUSH; parse_expr(Inc); *++code = MOD; type = INT;}
        // 后缀自增/自减: var++, var--
        else if (token == Inc || token == Dec) {
            // 左操作数 (var) 应该已经解析，其值在 ax 中，地址在其 PUSH, LC/LI 序列中。
            // *code 指向 LC/LI
            // 语义：表达式结果是原值，变量自增/减。
            // 1. PUSH addr (if *code is LC/LI, change to PUSH)
            // 2. LC/LI (load original value to ax)
            // 3. PUSH ax (save original value for result of expression)
            // 4. PUSH IMM 1/8
            // 5. ADD/SUB (ax = original_value + 1/8, new value in ax)
            // 6. SC/SI (store new value back to var_addr)
            // 7. ax needs to be original value (from step 3). The PUSHed value.
            //    The current code calculates new_value in ax, then stores it.
            //    Then it PUSHes another 1/8 and SUB/ADDs to get original_value back in ax.
            if (*code == LC) {*code = PUSH; *++code = LC;} // PUSH var_addr; ax = var_val after LC
            else if (*code == LI) {*code = PUSH; *++code = LI;}
            else {
                printf("%lld: invlid operator (postfix ++/--) on non-lvalue\n", line);
                exit(-1);
            }
            // ax has original value. var_addr is on stack.
            *++code = PUSH; // PUSH original value (this will be the result of expr)
            *++code = IMM;
            *++code = (tmp_type > PTR) ? 8 : 1; // step (tmp_type is type of var)
            *++code = (token == Inc) ? ADD : SUB; // ax = var_val + step (new value in ax)
            *++code = (tmp_type == CHAR) ? SC : SI; // Store new value to var_addr
                                                    // stack: [var_addr] [original_val]
                                                    // SC/SI uses var_addr from stack, new_val from ax.
                                                    // after SC/SI, var_addr is popped. stack: [original_val]
                                                    // ax still holds new_val.
            // We need original value in ax. It was PUSHed.
            // The code below SUBTRACTS/ADDS step from new_value to get original_value.
            // This is a bit convoluted. Simpler: after SC/SI, pop original_val to ax.
            // Or, before SC/SI, if original_val is PUSHed to stack, then after SC/SI, that value is at stack top.
            // Then a POP to ax instruction would be needed. This VM doesn't have POP.
            // Current code:
            *++code = PUSH; // PUSH new_value (ax) again? No, this is to get the original value back.
                            // stack: [original_val]
                            // ax = new_val
                            // PUSH ax (new_val) -> stack: [original_val] [new_val]
            *++code = IMM;
            *++code = (tmp_type > PTR) ? 8 : 1; // step
            // if Inc: ax = new_val - step = original_val
            // if Dec: ax = new_val + step = original_val
            *++code = (token == Inc) ? SUB : ADD; // ax = new_val -/+ step = original_val
            tokenize(); // Eat Inc/Dec token
            // type of expression is type of var
            type = tmp_type;
        }
        // 数组下标: array[index]  (equivalent to *(array + index))
        else if (token == Brak) { // '['
            assert(Brak);    // Eat '['
            // Left operand (array, tmp_type) is already PUSHed or its value is in ax.
            // If it was an Id, its value (address for global, value for local if LC/LI happened)
            // or address (if LC/LI was changed to PUSH) is on stack.
            // For a[i], 'a' (array base address) should be on stack or computed into ax.
            // tmp_type is type of 'a'.
            // Code for 'a' has been generated. Its value is in ax.
            // We need to PUSH 'a' (base address).
            *++code = PUSH; // PUSH value of array expression (hopefully base address)
            parse_expr(Assign); // Parse index expression, result in ax.
            assert(']');    // Eat ']'

            // Scaling for index if array is not char*
            // tmp_type is type of array (e.g. INT + PTR for int*)
            // type of index (ax) is INT.
            if (tmp_type > PTR) { // If array is int*, char**, etc. (not char*)
                                  // then base type is not CHAR. Scale index.
                *++code = PUSH; // PUSH index value
                *++code = IMM;
                *++code = 8;    // sizeof element (assuming non-char pointers point to 8-byte things)
                *++code = MUL;  // ax = index * 8
            }
            else if (tmp_type < PTR) { // If array is not a pointer type (e.g. int[...])
                printf("line %lld: invalid index op (base is not a pointer)\n", line);
                exit(-1);
            }
            // Now ax = scaled_index. Stack top has array_base_address.
            *++code = ADD; // ax = array_base_address + scaled_index
            // Resulting type is the element type of the array
            type = tmp_type - PTR; // e.g., if tmp_type was INT+PTR, type becomes INT
            // Load value from the calculated address
            *++code = (type == CHAR) ? LC : LI;
        }
        else {
            printf("%lld: invlid token in expression (token=%lld '%c')\n", line, token, (char)token);
            exit(-1);
        }
    }
}

// 解析语句 (Statement Parser)
void parse_stmt() {
    int* a; // temp pointer for jump address (e.g. start of loop)
    int* b; // temp pointer for jump address (e.g. end of loop / else branch)

    // If 语句: if (expr) stmt1 [else stmt2]
    if (token == If) {
        assert(If);
        assert('(');
        parse_expr(Assign); // 解析条件表达式
        assert(')');
        // 条件表达式结果在 ax 中
        *++code = JZ;       // 如果 ax == 0 (false), 跳转到 else 部分或 if 结束
        b = ++code;         // 保存 JZ 指令的操作数地址，待回填

        parse_stmt();       // 解析 if 为 true 时执行的语句 (stmt1)

        if (token == Else) {
            assert(Else);
            // JZ (b) 应该跳转到 else 块之后 stmt1 结束的地方。
            // 不，JZ(b)应该跳转到 else 块的开始。
            // *b = (int)(code + 3);  // JZ (false) jumps to start of else block. (JMP, placeholder, else_code)
            //                        // code is now at end of stmt1.
            //                        // Need a JMP over else block if stmt1 was executed.
            // JZ target_else
            // stmt_true
            // JMP end_if
            // target_else:
            // stmt_else
            // end_if:

            // *b points to JZ's operand. It should jump to the start of the else statement.
            // The else statement starts after the JMP we are about to emit.
            // So, *b should be code + 3 (JMP, its_operand, start_of_else_stmt)
            *b = (int)(code + 3); // 回填 JZ 的跳转目标：跳到 else 语句块的起始处 (跳过下面的 JMP)

            *++code = JMP;    // 如果 stmt1 执行了，需要无条件跳过 else 语句块
            b = ++code;       // 保存 JMP 指令的操作数地址，待回填 (跳到 if-else 结构结束后的地址)

            parse_stmt();     // 解析 else 语句块 (stmt2)
        }
        // 回填 JZ (if no else) 或 JMP (if there was an else) 的跳转目标
        // 目标是整个 if 或 if-else 结构结束后的下一条指令
        *b = (int)(code + 1);
    }
    // While 循环: while (expr) stmt
    else if (token == While) {
        assert(While);
        a = code + 1;       // 记录循环条件的起始地址 (loop_start)

        assert('(');
        parse_expr(Assign); // 解析循环条件表达式
        assert(')');
        // 条件结果在 ax 中
        *++code = JZ;       // 如果 ax == 0 (false), 跳转到循环结束 (loop_end)
        b = ++code;         // 保存 JZ 指令的操作数地址，待回填

        parse_stmt();       // 解析循环体语句

        *++code = JMP;      // 无条件跳转回循环条件判断处
        *++code = (int)a;   // JMP 的目标是 loop_start

        *b = (int)(code + 1); // 回填 JZ 的跳转目标 (loop_end)，即 JMP 之后的指令
    }
    // Return 语句: return [expr];
    else if (token == Return) {
        assert(Return);
        if (token != ';') { // 如果 return 后面不是分号，说明有返回值表达式
            parse_expr(Assign); // 解析返回值表达式，结果在 ax 中
        }
        // 如果是 void return; 或者表达式解析完后，ax 中是返回值
        assert(';');
        *++code = RET;      // 生成 RET 指令
    }
    // 复合语句 (代码块): { stmt1 stmt2 ... }
    else if (token == '{') {
        assert('{');
        // 在 c4c 中，这里会引入新的作用域（符号表处理）
        // 这个版本似乎没有显式的局部作用域符号表管理在 parse_stmt 层面，
        // 局部变量是在 parse_fun 的开头声明的。
        // 块作用域的变量声明本编译器不支持。
        while (token != '}') {
            parse_stmt(); // 递归解析块内的语句
        }
        assert('}');
    }
    // 空语句: ;
    else if (token == ';') {
        assert(';');
    }
    // 表达式语句: expr;
    else {
        parse_expr(Assign); // 解析表达式
        assert(';');        // 期望分号
        // 表达式语句的值通常被丢弃，但其副作用会发生。ax 中有表达式结果，但通常不用。
        // 如果需要丢弃栈上的值（如果表达式 PUSH 了东西但最终没被 VM 指令消耗），需要 POP 指令，但这里没有。
        // 不过 parse_expr 生成的指令序列，通常最后结果在 ax，不会持续堆积在栈上除非是运算符的 PUSH。
    }
}

// 解析函数体
// Assumes '{' has just been consumed, or type declarations for locals are next.
// 函数的参数已在 parse_param 中处理完毕，并记录在符号表中，ibp 也已设置。
void parse_fun() {
    int type_of_var; // 局部变量的类型
    int i;         // 用于计算局部变量在栈帧中的偏移，从 ibp (参数个数) 开始递增

    i = ibp; // 局部变量的偏移从参数之后开始。
             // bp 由 NVAR 指令在虚拟机执行时自行处理。
             // 局部变量声明必须在函数体的最前面
    while (token == Char || token == Int) { // 解析局部变量声明
        type_of_var = parse_base_type(); // 获取基本类型 (char 或 int)
        while (token != ';') { // 一行可以声明多个同类型变量: int a, *b, c;
            // 解析指针类型 (如 int *p;)
            int current_var_type = type_of_var;
            while (token == Mul) {
                assert(Mul);
                current_var_type = current_var_type + PTR;
            }
            check_local_id(); // 检查变量名是否在当前函数作用域内重复声明
            assert(Id);       // 期望变量名

            hide_global();    // 如果局部变量名与全局变量同名，隐藏全局变量
            // 将局部变量信息存入符号表
            symbol_ptr[Class] = Loc;    // 类别为 Loc
            symbol_ptr[Type]  = current_var_type; // 类型
            // Value 存储的是相对于 bp 的偏移。
            // 参数偏移是 bp positive: bp+1, ..., bp+ibp (ibp = num_params)
            // 局部变量偏移是 bp negative (or zero): bp+0, bp-1, ...
            // i 从 ibp 开始，每次 ++i。
            // symbol_ptr[Value] = ++i;
            // LEA 指令操作数是 ibp - symbol_ptr[Value]
            // For first local var: Value = ibp+1. ibp - (ibp+1) = -1.  (bp-1)
            // For second local var: Value = ibp+2. ibp - (ibp+2) = -2. (bp-2)
            // 这是正确的，局部变量在 bp 向下（低地址）分配。
            symbol_ptr[Value] = ++i;

            if (token == ',') {
                assert(',');
            }
        }
        assert(';'); // 变量声明行以分号结束
    }

    // 生成 NVAR 指令，为所有局部变量分配栈帧空间
    *++code = NVAR;
    // NVAR 的操作数是需要分配的局部变量的 *数量* (不是字节数)
    // i 是最后一个局部变量的 Value (ibp + num_local_vars)
    // ibp 是参数数量 (如果从0计数，则是 num_params)
    //   或者是在 parse_param 后设置的 param_count.
    // 在 parse_param 中: `ibp = ++i;` (i 是参数个数)
    // 在 parse_fun 中: `i = ibp;` (i 初始为参数个数)
    //                  `symbol_ptr[Value] = ++i;` (所以第一个局部变量 value 是 param_count+1)
    // num_local_vars = (final i) - (initial i) = (final i) - ibp
    *++code = i - ibp; // 分配的槽位数量

    // 解析函数体内的语句，直到遇到 '}'
    // 函数体本身不是由 '{' '}' 包围的，而是在 `parse()` 中 `assert('{')` 后调用 `parse_fun()`
    // 然后 `parse_fun()` 期望 `parse_stmt()` 处理语句，直到 `keyword()` 中 `main_ptr` 之后 `src = src_dump`，
    // 或者 `parse()` 的主循环 `while (token > 0)` 遇到文件尾。
    // 不对，`parse()` 中调用 `parse_fun()` 之后应该有 `assert('}')`。
    // `parse_fun()` 应该解析到函数的 `}` 为止。
    // `parse()`: `assert('{'); parse_fun();` -> 之后应该有 `assert('}')`，但没有。
    // `parse_fun()` 内部的 `while (token != '}') parse_stmt();` 会处理。
    while (token != '}') { // 函数定义以 '}' 结束
        parse_stmt();
    }

    // 如果函数最后一条指令不是 RET (例如没有显式 return 的 void 函数)，则添加一个 RET
    if (*code != RET) {
        *++code = RET;
    }

    // 函数结束，恢复可能被局部变量覆盖的全局变量的符号表信息
    symbol_ptr = symbol_table; // 从符号表头开始遍历
    while (symbol_ptr[Token]) { // 直到符号表末尾
        if (symbol_ptr[Class] == Loc && symbol_ptr[GClass] != 0) { // 如果是局部变量且之前隐藏了全局变量
            recover_global(); // 恢复它
            symbol_ptr[GClass] = 0; // 清理备份，表示已恢复
        }
        symbol_ptr = symbol_ptr + SymSize;
    }
}

// 主解析函数：驱动整个编译过程 (词法分析 -> 语法分析 -> 代码生成)
void parse() {
    int base_type; // 用于存储变量或函数声明的基本类型 (char/int)
    int type;      // 用于存储变量或函数的完整类型 (包括指针)
    // int* p; // 未使用的指针

    line = 1;  // 初始化行号
    token = 1; // 初始化 token (非0即可，保证循环至少执行一次)

    // 循环处理源代码中的顶层声明，直到文件结束 (token变为0)
    while (token > 0) {
        tokenize(); // 获取下一个词法单元

        if (token == 0) break; // 文件结束

        // 解析 enum 声明: enum [name] { ... };
        if (token == Enum) {
            assert(Enum);
            if (token != '{') { // 如果 enum 后面不是 '{', 说明可能有 enum 名
                assert(Id);   // 期望一个标识符 (enum 的名称)，但这里简单跳过
            }
            assert('{');      // 期望 '{'
            parse_enum();     // 解析 enum 体内的成员
            assert('}');      // 期望 '}'
        }
        // 解析变量声明或函数定义 (以 int 或 char 开头)
        else if (token == Int || token == Char) {
            base_type = parse_base_type(); // 获取基本类型

            // 一行可以声明多个变量或一个函数: e.g. int a, b, foo();
            // 处理直到遇到 ';' (变量声明结束) 或 '}' (可能是解析错误或文件意外结束)
            // 实际上，函数定义后不需要 ';'
            while (token != ';' && token != '}' && token > 0) {
                type = base_type; // 当前声明的类型初始化为基本类型
                // 解析指针声明 (如 int *p, char **s)
                while (token == Mul) { // Mul 也代表 '*'
                    assert(Mul);
                    type = type + PTR; // 每多一个 '*'，类型值加 PTR
                }

                check_new_id(); // 检查标识符是否为新的 (未声明过)
                assert(Id);     // 期望标识符 (变量名或函数名)
                                // symbol_ptr 指向此 Id 在符号表中的新条目

                symbol_ptr[Type] = type; // 设置符号的类型

                if (token == '(') { // 如果标识符后是 '('，则为函数定义/声明
                    symbol_ptr[Class] = Fun;    // 类别为 Fun
                    symbol_ptr[Value] = (int)(code + 1); // 函数的值是其入口地址 (下一条指令)

                    assert('(');
                    parse_param(); // 解析函数参数列表
                    assert(')');

                    assert('{');   // 函数体必须以 '{' 开始
                    parse_fun();   // 解析函数体
                    assert('}');   // 函数体必须以 '}' 结束
                                   // 注意：标准C中函数定义后不跟分号
                } else { // 否则为全局变量声明
                    symbol_ptr[Class] = Glo;    // 类别为 Glo
                    symbol_ptr[Value] = (int)data; // 全局变量的值是其在数据段的地址
                    data = data + 8;        // 为该全局变量分配8字节空间 (所有类型都占8字节)

                    // 如果一行有多个变量声明，用逗号分隔: int a, b;
                    // 这里 `while (token != ';')` 的循环应该在外面处理逗号。
                    // 当前结构: `int ident` (var) or `int ident (` (func)
                    // if var, expect `,` or `;`
                }
                // 处理一行中的多个声明，如 `int a, b;` 或 `int foo(), bar;` (后者非法)
                if (token == ',') {
                    assert(','); // 如果是逗号，则准备解析下一个声明 (在同一个 base_type下)
                } else {
                    // 如果不是逗号，对于全局变量，应该期待分号。
                    // 对于函数，已经 assert('}') 了，这里应该结束当前声明。
                    // 这个 break 会跳出 `while (token != ';' && token != '}')`
                    // 然后外层 `tokenize()` 会获取 ';' (如果是变量) 或其他。
                    break;
                }
            }
            // 全局变量声明必须以 ';' 结束
            if (symbol_ptr[Class] == Glo) { // 检查是否是变量声明的结束
                 // `while (token != ';' ...)` 结束后，如果是因为 `break` (遇到非逗号)，
                 // 那么这里需要消耗那个终结符，通常是';'
                 // 如果是因为 token == ';' 跳出循环，那么 assert(';') 会消耗它。
                 // 这块逻辑有点微妙。上面的 break 应该在 ',' 之后。
                 // A better loop structure:
                 // do {
                 //   ... parse one declarator (var or func) ...
                 //   if (token == ',') { assert(','); continue; }
                 //   else { break; }
                 // } while (true);
                 // if (class_was_global_var) assert(';');

                 // 当前的结构，如果 `int a, b;`，在解析完 b 之后，token 是 ';'
                 // 循环条件 `token != ';'` 不满足，退出。然后下面的 `assert(';')`
            }
            if (token == ';') { // 全局变量声明或空声明语句以分号结束
                assert(';');
            }

        } else if (token != 0) { // 如果不是顶层声明也不是文件结束，则语法错误
             printf("line %lld: syntax error at top level, token %lld (%c)\n", line, token, (char)token);
             exit(-1);
        }
    }
}


// 初始化关键字和内建函数到符号表
void keyword() {
    int i;
    // 源代码字符串，包含所有关键字和内建函数名
    src = "char int enum if else return sizeof while " // Keywords
          "open read close printf malloc free memset memcmp exit void main"; // Built-in functions + void/main

    // 将关键字添加到符号表
    i = Char; // Enum value for 'char'
    while (i <= While) { // Iterate through keyword enum values
        tokenize(); // 从 src 字符串中解析一个词 (即关键字)
        symbol_ptr[Token] = i++; // 设置符号表条目的 Token 字段为对应的枚举值
    }

    // 将内建系统调用函数添加到符号表
    i = OPEN; // Enum value for 'open' (from instruction set enum)
    while (i <= EXIT) { // Iterate through built-in function instruction enum values
        tokenize(); // 解析一个内建函数名
        symbol_ptr[Class] = Sys;        // 类别为 Sys (系统调用)
        symbol_ptr[Type]  = INT;        // 假设所有内建函数返回 INT (或句柄等效于INT)
        symbol_ptr[Value] = i++;        // Value 字段存储的是对应虚拟机的指令码
    }

    // 特殊处理 void 和 main
    tokenize(); // 解析 "void"
    symbol_ptr[Token] = Char; // 'void' 在类型系统中被当作 'char' 处理 (可能是为了简化，或表示不关心类型)
                              // 或者应该有一个 TVOID 类型。这里用 Char 可能是个简化。
                              // 在函数返回类型中，void 函数的 Type 可能是 0 (CHAR)。

    tokenize(); // 解析 "main"
    main_ptr = symbol_ptr; // 保存 'main' 函数在符号表中的指针，虚拟机执行时从这里开始
                           // main 本身也是一个 Fun，其 Class 和 Value 会在 parse() 中被设置

    src = src_dump; // 恢复源代码指针到实际用户代码的开头
}

// 初始化虚拟机（分配内存段）
int init_vm() {
    // 为代码段分配内存
    if (!(code = code_dump = malloc(MAX_SIZE))) {
        printf("could not malloc(%lld) for code segment\n", MAX_SIZE);
        return -1;
    }
    // 为数据段分配内存
    if (!(data = malloc(MAX_SIZE))) {
        printf("could not malloc(%lld) for data segment\n", MAX_SIZE);
        return -1;
    }
    // 为栈段分配内存
    if (!(stack = malloc(MAX_SIZE))) {
        printf("could not malloc(%lld) for stack segment\n", MAX_SIZE);
        return -1;
    }
    // 为符号表分配内存 (MAX_SIZE / 16，假设平均每个符号表项占16字节，或者只是一个比例)
    // SymSize 是字段数，每个字段是 int (8字节)。所以是 MAX_SIZE / (SymSize * 8)
    // 这里是 MAX_SIZE / 16，如果 SymSize 是 10，那么每个符号是 80 字节。
    // 这个大小可能不足够，或者 MAX_SIZE 很大。
    // 假设 MAX_SIZE / 16 是字节数。
    if (!(symbol_table = malloc(MAX_SIZE / 16))) {
        printf("could not malloc(%lld) for symbol_table\n", MAX_SIZE / 16);
        return -1;
    }

    // 初始化内存段为0
    memset(code, 0, MAX_SIZE);
    memset(data, 0, MAX_SIZE);
    memset(stack, 0, MAX_SIZE);
    memset(symbol_table, 0, MAX_SIZE / 16); // 符号表也清零

    // bp 和 sp 初始化。sp 指向栈顶 (高地址)，bp 也指向栈顶。
    // 栈向下增长 (地址变小)。
    bp = sp = (int*)((int)stack + MAX_SIZE); // 指向栈内存区域的末尾 (最高地址)

    return 0;
}

// 运行虚拟机，执行已编译的代码
// argc, argv 是传递给 main 函数的参数
int run_vm(int argc, char** argv) {
    int op;   // 当前操作码 (指令)
    int* tmp; // 临时指针，用于某些指令的操作

    // 设置 main 函数的返回地址和参数
    // 栈向下增长，所以先压入 argc, 再压 argv, 再压入 argc 和 argv 的地址作为参数给 C 的 main
    // 实际上，这个虚拟机的 main 函数的参数处理方式与标准 C 不同。
    // 它期望栈上有: [EXIT_opcode] [argc] [argv_ptr] (argv 是 char**)
    // 模拟 main 的调用帧:
    // 1. 为 main 返回后执行 EXIT 指令压栈 (这是整个程序结束的标记)
    *--sp = EXIT; // main 函数执行完毕后，会 "返回" 到这里，执行 EXIT

    // 2. 压入 main 的参数 (argc, argv)
    //    VM 的 CALL 指令会保存返回地址。这里是直接启动 main，所以自己构造。
    //    c4 的 main 调用：`*--sp = argc; *--sp = (int)argv; *--sp = PUSH;`
    //    然后 `pc = main_ptr[Value]`
    //    这里的 PUSH 似乎是多余的，或者有特殊含义。
    //    原始 c4c 的 main 调用栈准备:
    //    *--SP = argc;
    //    *--SP = (int)argv;
    //    *--SP = PUSH; // for setup code in main to pop
    //    PC = pool[main_ptr[Value]];
    // 这里似乎简化了：
    *--sp = PUSH; // Push a dummy value or an indicator for printf?
    tmp = sp;     // Store current sp for printf args.
                  // This seems to be for a potential `printf("%s %s ...", argv[0], argv[1]...)` scenario
                  // if main immediately calls printf. But printf arg passing is complex.
                  // Let's look at c4's bootstrap or printf handling.
                  // Original c4: *--sp = argc; *--sp = (int)argv; then pc points to main.
                  // main then has ENT (NVAR) to make space.
                  // This setup is a bit different.
    *--sp = argc;
    *--sp = (int)argv;
    // *--sp = (int)tmp; // This seems to be for varargs for printf, if main's first call is printf.
                       // It seems like it's trying to pass 3 arguments to a potential initial call.
                       // If main is `int main(int argc, char *argv[])`, it uses its parameters.
                       // This stack setup is non-standard for the VM's own function call mechanism.
                       // Let's assume it's a special setup for `main`.

    // 获取 main 函数的入口地址
    if (!(pc = (int*)main_ptr[Value])) {
        printf("main function is not defined\n");
        exit(-1);
    }

    cycle = 0; // 指令周期计数器清零
    // 虚拟机主执行循环
    while (1) {
        cycle++;
        op = *pc++; // 获取当前指令，pc 指向下一条

        // 根据操作码执行相应操作
        if (op == IMM)          ax = *pc++;                     // ax = immediate_value / global_address
        else if (op == LEA)     ax = (int)(bp + *pc++);         // ax = bp + offset (address of local var/param)
        else if (op == LC)      ax = *(char*)ax;                // ax = *(char*)ax (load char from address in ax)
        else if (op == LI)      ax = *(int*)ax;                 // ax = *(int*)ax (load int from address in ax)
        else if (op == SC)      *(char*)*sp++ = ax;             // *(char*)*sp = ax; sp++ (store char in ax to address from stack; pop address)
        else if (op == SI)      *(int*)*sp++ = ax;              // *(int*)*sp = ax; sp++ (store int in ax to address from stack; pop address)
        else if (op == PUSH)    *--sp = ax;                     // --sp; *sp = ax (push ax onto stack)
        // 跳转指令
        else if (op == JMP)     pc = (int*)*pc;                 // pc = target_address
        else if (op == JZ)      pc = ax ? pc + 1 : (int*)*pc;   // if (ax == 0) pc = target_address; else pc++ (skip target)
        else if (op == JNZ)     pc = ax ? (int*)*pc : pc + 1;   // if (ax != 0) pc = target_address; else pc++ (skip target)
        // 算术和逻辑运算 (结果存入 ax, 操作数来自栈顶和 ax)
        // op ax, stack_top -> ax ; sp++
        else if (op == OR)      ax = *sp++ |  ax;
        else if (op == XOR)     ax = *sp++ ^  ax;
        else if (op == AND)     ax = *sp++ &  ax;
        else if (op == EQ)      ax = *sp++ == ax;
        else if (op == NE)      ax = *sp++ != ax;
        else if (op == LT)      ax = *sp++ <  ax;
        else if (op == LE)      ax = *sp++ <= ax;
        else if (op == GT)      ax = *sp++ >  ax;
        else if (op == GE)      ax = *sp++ >= ax;
        else if (op == SHL)     ax = *sp++ << ax;
        else if (op == SHR)     ax = *sp++ >> ax;
        else if (op == ADD)     ax = *sp++ +  ax;
        else if (op == SUB)     ax = *sp++ -  ax;
        else if (op == MUL)     ax = *sp++ *  ax;
        else if (op == DIV)     ax = *sp++ /  ax;
        else if (op == MOD)     ax = *sp++ %  ax;
        // 函数调用相关指令
        // CALL target_addr: push (pc+1) (return address); pc = target_addr
        else if (op == CALL)    {*--sp = (int)(pc+1); pc = (int*)*pc;}
        // NVAR num_locals: push bp; bp = sp; sp = sp - num_locals (allocate space for locals)
        else if (op == NVAR)    {*--sp = (int)bp; bp = sp; sp = sp - *pc++;}
        // DARG num_args: sp = sp + num_args (discard arguments from stack after call)
        else if (op == DARG)    sp = sp + *pc++;
        // RET: sp = bp (restore sp to frame base); bp = *sp++ (restore old bp); pc = *sp++ (restore return address)
        else if (op == RET)     {sp = bp; bp = (int*)*sp++; pc = (int*)*sp++;}
        // 系统调用
        // 参数从栈上传递，sp[0] 是最后一个参数，sp[1] 是倒数第二个，以此类推。
        // 返回值在 ax 中。
        else if (op == OPEN)    ax = open((char*)sp[1], sp[0]); // open(filename, flags)
        else if (op == CLOS)    ax = close(sp[0]);              // close(fd)
        else if (op == READ)    ax = read(sp[2], (char*)sp[1], sp[0]); // read(fd, buf, count)
        // PRTF (printf) 的参数处理比较特殊：
        // pc[0] (即 *pc) 是 DARG 的数量，由编译器生成。pc[1] 似乎不应该被访问。
        // printf((char*)tmp[0], tmp[-1], tmp[-2], tmp[-3], tmp[-4], tmp[-5])
        // tmp = sp + pc[1] - 1;  pc[1] is an immediate operand for PRTF.
        // Let's assume pc[0] is the number of arguments for printf (including format string).
        // The arguments are on stack: sp[0] is last, sp[pc[0]-1] is format string.
        // tmp = sp + num_args - 1 (points to the format string on stack)
        // tmp[0] is format_string
        // tmp[-1] is arg1
        // tmp[-2] is arg2 etc.
        // This matches how varargs are typically laid out on stack if pushed in reverse order.
        else if (op == PRTF)    {tmp = sp + pc[0] -1; ax = printf((char*)tmp[0], tmp[-1], tmp[-2], tmp[-3], tmp[-4], tmp[-5]); pc++;} // pc[0] is arg_count
        else if (op == MALC)    ax = (int)malloc(sp[0]);        // malloc(size)
        else if (op == FREE)    {free((void*)sp[0]); ax = 0;}   // free(ptr)
        else if (op == MSET)    ax = (int)memset((char*)sp[2], sp[1], sp[0]); // memset(dest, val, count)
        else if (op == MCMP)    ax = memcmp((char*)sp[2], (char*)sp[1], sp[0]);  // memcmp(ptr1, ptr2, count)
        else if (op == EXIT)    {printf("exit(%lld)\n", *sp); return *sp;} // exit(code)
        // 未知指令
        else {
            printf("unkown instruction: %lld, cycle: %lld\n", op, cycle);
            return -1; // 执行失败
        }
    }
    return 0; // 正常情况下，虚拟机通过 EXIT 指令退出，不会执行到这里
}

char* insts; // 用于调试输出指令名称的字符串

// 将生成的虚拟机指令以汇编助记符形式写入文件 "assemble" (用于调试)
void write_as() {
    int fd;        // 文件描述符
    char* buffer;  // 用于格式化输出的缓冲区
    // 包含所有指令助记符的字符串，每个助记符占4个字符，后跟一个逗号
    insts = "IMM ,LEA ,JMP ,JZ  ,JNZ ,CALL,NVAR,DARG,RET ,LI  ,LC  ,SI  ,SC  ,PUSH,"
            "OR  ,XOR ,AND ,EQ  ,NE  ,LT  ,GT  ,LE  ,GE  ,SHL ,SHR ,ADD ,SUB ,MUL ,DIV ,MOD ,"
            "OPEN,READ,CLOS,PRTF,MALC,FREE,MSET,MCMP,EXIT,";

    // 打开 (或创建) "assemble" 文件用于写入
    // 0x0001 (O_WRONLY) | 0x0200 (O_CREAT) | 0x0400 (O_TRUNC would be good too)
    // The flags are platform specific. Standard flags: O_WRONLY | O_CREAT | O_TRUNC
    fd = open("assemble", O_WRONLY | O_CREAT | O_TRUNC, 0644); // Added O_TRUNC and mode
    if (fd < 0) { perror("open assemble file failed"); return; }

    buffer = malloc(100); // 分配缓冲区
    if (!buffer) { close(fd); perror("malloc for buffer failed"); return; }

    // code_dump 初始指向代码段的开头 (与 code 相同)
    // code 指向已生成指令的末尾的下一个位置
    int* current_instr_ptr = code_dump; // Use a new pointer to iterate
    while (current_instr_ptr < code) {
        int instr_val = *current_instr_ptr;
        // 格式化输出: (地址/序号) 指令助记符
        // (current_instr_ptr - code_dump) gives the offset/index
        sprintf(buffer, "(%04lld) %-4.4s", (long long)(current_instr_ptr - code_dump), insts + (instr_val * 5));
        write(fd, buffer, strlen(buffer));

        current_instr_ptr++; // 指向指令的操作数或下一条指令

        // 如果指令带有操作数 (IMM, LEA, JMP, JZ, JNZ, CALL, NVAR, DARG, PRTF's arg count)
        // RET 及之后(LI,LC,SI,SC,PUSH, ... EXIT) 的指令没有立即数操作数跟随在 pc++ 之后
        // (除了PRTF的特殊操作数)
        // Original logic: if (*code_dump < RET) -> this is about the enum value, not instruction type.
        // A better check:
        if (instr_val == IMM || instr_val == LEA || instr_val == JMP || instr_val == JZ || instr_val == JNZ ||
            instr_val == CALL || instr_val == NVAR || instr_val == DARG || instr_val == PRTF) {
            sprintf(buffer, " %lld\n", *current_instr_ptr);
            current_instr_ptr++; // 消耗操作数
        } else {
            buffer[0] = '\n';
            buffer[1] = '\0';
        }
        write(fd, buffer, strlen(buffer));
    }
    free(buffer);
    close(fd);
}

// 从文件加载源代码到内存
int load_src(char* file) {
    int fd;  // 文件描述符
    int cnt; // 读取的字节数

    // 使用 open/read/close 系统调用打开并读取源文件
    if ((fd = open(file, O_RDONLY)) < 0) { // O_RDONLY for read-only
        printf("could not open source code(%s)\n", file);
        perror("open failed");
        return -1;
    }
    // 为源代码分配内存
    // src 和 src_dump 指向同一块内存
    if (!(src = src_dump = malloc(MAX_SIZE))) {
        printf("could not malloc(%lld) for source code\n", MAX_SIZE);
        close(fd);
        return -1;
    }
    // 读取文件内容到 src 指向的内存
    // 最多读取 MAX_SIZE - 1 字节，为末尾的 '\0' 留出空间
    if ((cnt = read(fd, src, MAX_SIZE - 1)) <= 0) {
        printf("could not read source code (read %lld bytes)\n", cnt);
        perror("read failed");
        free(src);
        close(fd);
        return -1;
    }
    src[cnt] = 0; // 在源代码末尾添加字符串结束符 '\0'
    close(fd);    // 关闭文件
    return 0;
}

// 主函数
// 注意：main 的参数类型是 int32_t，但在程序开始处 #define int int64_t
// 这意味着实际传递给 main 的 argc, argv 可能是32位，但内部作为64位处理。
// 为了兼容性或特定平台考虑。
int32_t main(int32_t argc_orig, char** argv_orig) {
    // 将32位参数转换为内部使用的64位类型
    int argc = argc_orig;
    char** argv = argv_orig;

    MAX_SIZE = 128 * 1024 * 8; // 设置最大内存为 1MB (128K 个 64位整数)

    // 检查命令行参数
    if (argc < 2) {
        printf("Usage: %s <source_file.c>\n", argv[0]);
        return -1;
    }

    // 加载源代码文件
    if (load_src(*(argv+1)) != 0) { // argv[1] 是源文件名
        return -1;
    }

    // 初始化虚拟机内存和寄存器
    if (init_vm() != 0) {
        return -1;
    }

    // 初始化关键字和内建函数到符号表
    keyword();

    // 解析源代码，生成虚拟机指令 (存入 code 段)
    parse();

    // (可选) 将生成的虚拟机指令以汇编形式写入文件 (用于调试)
    // write_as(); // 取消注释以启用汇编输出

    // 运行虚拟机，执行生成的指令
    // main 函数的参数是 (argc-1, argv+1)，模拟 C 程序接收参数的方式
    // 即 argv[0] 是程序名，argv[1] 之后是实际参数。
    // 如果虚拟机里的 main 函数也期望这种形式，则这样传递。
    // 但 run_vm 内部对 main 的调用是直接使用原始的 argc, argv。
    // 这里 --argc, ++argv 是为了如果虚拟机里的main函数签名是 main(actual_argc, actual_argv)
    // 那么这里的调整是把程序名去掉。
    return run_vm(--argc, ++argv);
}
