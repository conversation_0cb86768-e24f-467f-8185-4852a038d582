// 基础语法测试 - 验证基本功能
program BasicTest {
    func add(a, b) {
        return a + b;
    }
    
    func factorial(n) {
        let result = 1;
        if (n > 1) {
            result = n * factorial(n - 1);
        };
        return result;
    }
    
    main {
        let x = 10;
        let y = 20;
        let sum = add(x, y);
        
        output(sum);
        
        let fact = factorial(5);
        output(fact);
        
        // 测试控制流
        let i = 0;
        while (i < 5) {
            output(i);
            i = i + 1;
        };
        
        // 测试条件语句
        if (sum > 25) {
            output(999);
        } else {
            output(111);
        };
    }
}
