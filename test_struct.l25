program StructTest {

    struct Point {
        x;
        y;
    }

    func identity(val) {
        return val;
    }

    main {
        let p : Point;
        let result; 

        p.x = 10;
        p.y = 20;

        output(p.x); 
        output(p.y); 

        result = identity(p.x);
        output(result); 

        p.y = p.x + 5; 
        output(p.y); 

        result = identity(p.y);
        output(result); 
    }
}