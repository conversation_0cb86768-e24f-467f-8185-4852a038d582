program TestRecursionFibonacci {

  // Recursive function to calculate the Nth Fibonacci number
  func fibonacci(n) {
    let result;

    if (n <= 0) {
      result = 0; // Base case: F(0) = 0
    } else {
      if (n == 1) {
        result = 1; // Base case: F(1) = 1
      } else {
        // Recursive step: F(n) = F(n-1) + F(n-2)
        let fibNMinus1;
        let fibNMinus2;

        fibNMinus1 = fibonacci(n - 1); // Recursive call
        fibNMinus2 = fibonacci(n - 2); // Recursive call

        result = fibNMinus1 + fibNMinus2;
      }; // End of inner if-else (n != 1)
    }; // End of outer if-else (n > 0)
    return result;
  }

  main {
    output(fibonacci(10));
  }
}