#include <fcntl.h>
#include <memory.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// Use a specific typedef for 64-bit integers within the L25 language and VM.
// This is much safer and more portable than a global `#define int`.
typedef int64_t l25_int;

/* ========== Global Variables Definition ========== */
int MAX_SIZE; // Max size in BYTES for memory segments.

/* Virtual Machine's Memory Segments */
l25_int *code;       // Code segment: stores compiled VM instructions.
l25_int *code_dump;  // Code segment backup: for debugging output.
l25_int *stack;      // Stack segment: runtime data stack.

/* Pointers for Boundary Checks */
l25_int *code_end;
l25_int *stack_start;

/* Virtual Machine's Registers */
l25_int *pc; // Program Counter: points to the current instruction.
l25_int *sp; // Stack Pointer: points to the top of the stack.
l25_int *bp; // Base Pointer: points to the base of the current stack frame.

l25_int ax;    // General-purpose register: for calculations and temporary data.
l25_int cycle; // Instruction execution counter.

/* Lexer and Parser Global State */
char *src;      // Source code pointer: points to the current character being analyzed.
char *src_dump; // Source code backup: original start address of the source code.

l25_int *symbol_table;      // Symbol table: stores info about all identifiers.
l25_int *symbol_ptr;        // Symbol table pointer: points to the current symbol table entry.
l25_int *symbol_table_end;  // Symbol table boundary pointer for overflow checks.

l25_int token;     // Current token type.
l25_int token_val; // Current token value (if it's a number).
int line;      // Current line number for error reporting (standard int is sufficient).

/* L25 Language-specific Parser and VM State */
l25_int *entry_main_addr = 0;       // main() function entry address.
l25_int current_func_var_count = 0; // Number of local variables in the current function.
int current_parsing_func_body = 0; // Flag: 1 if parsing a function body, 0 otherwise.

/* Function Parameter Parsing */
#define MAX_PARAMS 10
l25_int *param_sym_entries[MAX_PARAMS]; // Stores symbol entries for function parameters.

/* Struct Definition */
#define MAX_STRUCT_MEMBERS 20
#define MAX_STRUCTS 10
l25_int *struct_members[MAX_STRUCTS][MAX_STRUCT_MEMBERS]; // Stores member symbol entries for each struct.
int struct_count = 0;                            // Number of defined structs.

/* Security and Performance Constants */
#define MAX_IDENTIFIER_LENGTH 255        // 最大标识符长度
#define MAX_SYMBOL_LOOKUPS 10000        // 最大符号查找次数（防DoS）
#define MAX_EXECUTION_CYCLES 100000000  // 最大执行周期数（防无限循环）
#define MAX_ARRAY_SIZE 10000            // 最大数组大小
#define FNV_PRIME 1099511628211ULL      // FNV-1a哈希质数

/* ========== Enumerations ========== */

// VM Instruction Set
enum {
    // Basic Data Operations
    IMM, LEA, JMP, JZ, JNZ,
    // Function Call
    CALL, NVAR, DARG, RET,
    // Memory Access
    LI, SI,
    // Stack Operations
    PUSH,
    // Arithmetic Operations
    ADD, SUB, MUL, DIV,
    // Comparison Operations
    EQ, NE, LT, GT, LE, GE,
    // Program Control
    EXIT,
    // I/O
    READ_INT_AX, PRINT_INT_AX,
    // Array and Struct Access
    LEA_ARRAY, LEA_STRUCT
};

// L25 Language Token Types
enum {
    Num = 128, Fun, Loc, Struct, Array, Id,
    Program, FuncKw, MainKw, Let, If, Else, Return, While, Input, Output, StructKw,
    Assign, Add, Sub, Mul, Div, Eq, Ne, Lt, Gt, Le, Ge,
    Dot, Colon
};

// Symbol Table Entry Fields
enum {
    Token, Hash, Name, Class, Type, Value, Extra, SymSize
};

// L25 Supported Data Types
enum {
    INT_TYPE, STRUCT_TYPE, ARRAY_TYPE
};

/* ========== Lexical Analyzer (Tokenizer) ========== */
void tokenize() {
    char *ch_ptr;
    l25_int current_hash_val;

    while ((token = *src++)) {
        if (token == '\n') {
            line++;
        } else if (token == ' ' || token == '\t' || token == '\r') {
        } else if ((token >= 'a' && token <= 'z') || (token >= 'A' && token <= 'Z') || (token == '_')) {
            ch_ptr = src - 1;
            current_hash_val = token;
            int identifier_len = 0;
            while ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z') || (*src >= '0' && *src <= '9') || (*src == '_')) {
                identifier_len++;
                if (identifier_len > MAX_IDENTIFIER_LENGTH) {  // 防止过长标识符攻击
                    printf("line %d: error: identifier too long (max %d characters)\n", line, MAX_IDENTIFIER_LENGTH);
                    exit(-1);
                }
                // 使用更好的哈希函数（FNV-1a变种）
                current_hash_val = (current_hash_val ^ *src++) * FNV_PRIME;
            }
            current_hash_val = (current_hash_val << 6) + (src - ch_ptr);

            symbol_ptr = symbol_table;
            int lookup_count = 0;  // 防止无限循环
            while (symbol_ptr[Token] && symbol_ptr < symbol_table_end) {
                lookup_count++;
                if (lookup_count > MAX_SYMBOL_LOOKUPS) {  // 防止DoS攻击
                    printf("line %d: fatal error: symbol table lookup limit exceeded\n", line);
                    exit(-1);
                }
                if (current_hash_val == symbol_ptr[Hash] && !memcmp((char *)symbol_ptr[Name], ch_ptr, src - ch_ptr)) {
                    token = symbol_ptr[Token];
                    return;
                }
                symbol_ptr = symbol_ptr + SymSize;
            }

            // BOUNDARY CHECK: Prevent symbol table overflow.
            if (symbol_ptr >= symbol_table_end) {
                printf("line %d: fatal error: symbol table overflow\n", line);
                exit(-1);
            }
            symbol_ptr[Name] = (l25_int)(uintptr_t)ch_ptr;
            symbol_ptr[Hash] = current_hash_val;
            token = symbol_ptr[Token] = Id;
            return;
        }
        else if (token >= '0' && token <= '9') {
            token_val = token - '0';
            while (*src >= '0' && *src <= '9') {
                // 防止整数溢出攻击
                if (token_val > (INT64_MAX - (*src - '0')) / 10) {
                    printf("line %d: error: number too large (overflow risk)\n", line);
                    exit(-1);
                }
                token_val = token_val * 10 + *src++ - '0';
            }
            token = Num;
            return;
        }
        else if (token == '/') {
            if (*src == '/') { // Line comment
                while (*src != 0 && *src != '\n') src++;
            } else {
                token = Div; return;
            }
        }
        else if (token == '=') { if (*src == '=') { src++; token = Eq; } else { token = Assign; } return; }
        else if (token == '!') { if (*src == '=') { src++; token = Ne; } else { printf("line %d: error: unexpected character '!'\n", line); exit(-1); } return; }
        else if (token == '<') { if (*src == '=') { src++; token = Le; } else { token = Lt; } return; }
        else if (token == '>') { if (*src == '=') { src++; token = Ge; } else { token = Gt; } return; }
        else if (token == '+') { token = Add; return; }
        else if (token == '-') { token = Sub; return; }
        else if (token == '*') { token = Mul; return; }
        else if (token == '.') { token = Dot; return; }
        else if (token == ':') { token = Colon; return; }
        else if (token == '(' || token == ')' || token == '{' || token == '}' || token == ',' || token == ';' || token == '[' || token == ']') {
            return;
        }
    }
}

/* ========== Parser Helper Functions ========== */

// Helper to check if code generation will overflow the code segment.
void check_code_overflow(int instructions_to_add) {
    if (code + instructions_to_add >= code_end) {
        printf("line %d: fatal error: code segment overflow\n", line);
        exit(-1);
    }
}

// Helper to validate program counter address
int is_valid_pc_address(l25_int *addr) {
    return (addr >= code_dump && addr < code_end);
}

// Helper to validate memory address for stack operations
int is_valid_stack_address(l25_int *addr) {
    return (addr >= stack_start && addr < (l25_int *)((uintptr_t)stack + MAX_SIZE));
}

void assert(l25_int tk) {
    if (token != tk) {
        printf("line %d: error: expected token %lld, but got %lld\n", line, tk, token);
        exit(-1);
    }
    tokenize();
}

/* ========== L25 Parser Functions ========== */
void parse_expr();
void parse_stmt();
void parse_stmt_list();
void parse_struct_def();
int find_struct_member(l25_int *struct_sym, l25_int *member_sym);

void parse_factor() {
    l25_int *id_sym_entry;

    if (token == Id) {
        id_sym_entry = symbol_ptr;
        tokenize();

        if (token == '(') { // Function call
            assert('(');
            int arg_count = 0;
            if (token != ')') {
                while (1) {
                    parse_expr();
                    check_code_overflow(1); *++code = PUSH;
                    arg_count++;
                    if (token == ',') assert(','); else break;
                }
            }
            assert(')');

            if (id_sym_entry[Class] != Fun) {
                printf("line %d: error: '%s' is not a function.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }

            check_code_overflow(4);
            *++code = CALL; *++code = id_sym_entry[Value];
            if (arg_count > 0) {
                *++code = DARG; *++code = arg_count;
            }
        } else if (token == '[') { // Array access
            if (id_sym_entry[Class] != Loc || id_sym_entry[Type] != ARRAY_TYPE) {
                printf("line %d: error: '%s' is not an array.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            assert('['); parse_expr(); assert(']');
            check_code_overflow(5);
            *++code = PUSH;
            *++code = LEA; *++code = id_sym_entry[Value];
            *++code = LEA_ARRAY;
            *++code = LI;
        } else if (token == Dot) { // Struct member access
            if (id_sym_entry[Class] != Loc || id_sym_entry[Type] != STRUCT_TYPE) {
                printf("line %d: error: '%s' is not a struct.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            assert(Dot); assert(Id);
            int member_offset = find_struct_member((l25_int*)id_sym_entry[Extra], symbol_ptr);
            if (member_offset < 0) {
                printf("line %d: error: struct member '%s' not found.\n", line, (char *)symbol_ptr[Name]);
                exit(-1);
            }
            check_code_overflow(5);
            *++code = LEA; *++code = id_sym_entry[Value];
            *++code = LEA_STRUCT; *++code = member_offset;
            *++code = LI;
        } else { // Simple variable access
            if (id_sym_entry[Class] != Loc) {
                printf("line %d: error: undefined variable '%s'.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            check_code_overflow(3);
            *++code = LEA; *++code = id_sym_entry[Value];
            *++code = LI;
        }
    } else if (token == Num) {
        check_code_overflow(2);
        *++code = IMM; *++code = token_val;
        tokenize();
    } else if (token == '(') {
        assert('(');
        parse_expr();
        assert(')');
    } else {
        printf("line %d: error: unexpected token in factor, got %lld.\n", line, token);
        exit(-1);
    }
}

void parse_term() {
    parse_factor();
    while (token == Mul || token == Div) {
        int op = token;
        tokenize();
        check_code_overflow(2);
        *++code = PUSH;
        parse_factor();
        *++code = (op == Mul) ? MUL : DIV;
    }
}

/*
 * 解析加减表达式（Sum Expression）
 * 功能：处理加法、减法运算和一元正负号
 */
void parse_sum_expr()
{
    int unary_op = 0; // 一元操作符标志：0=无，1=正号，2=负号

    // 处理一元正负号
    if (token == Add)
    {
        unary_op = 1; // 一元正号（实际上不需要特殊处理）
        tokenize();
    }
    else if (token == Sub)
    {
        unary_op = 2; // 一元负号
        tokenize();
    }

    if (unary_op == 2) { // 如果是一元负号
        check_code_overflow(3);
        *++code = IMM; *++code = 0;
        *++code = PUSH;
    }

    parse_term();

    if (unary_op == 2) {
        check_code_overflow(1);
        *++code = SUB;
    }

    while (token == Add || token == Sub)
    {
        int op = token;
        tokenize();
        check_code_overflow(2);
        *++code = PUSH;
        parse_term(); // 注意：这里不是 parse_sum_expr()，以避免递归问题
        *++code = (op == Add) ? ADD : SUB;
    }
}

void parse_expr() {
    parse_sum_expr();
    if (token == Eq || token == Ne || token == Lt || token == Le || token == Gt || token == Ge) {
        int op = token;
        tokenize();
        check_code_overflow(2);
        *++code = PUSH;
        parse_sum_expr();  // 修复：使用parse_sum_expr()而不是parse_expr()避免右结合性
        switch(op) {
            case Eq: *++code = EQ; break; case Ne: *++code = NE; break;
            case Lt: *++code = LT; break; case Le: *++code = LE; break;
            case Gt: *++code = GT; break; case Ge: *++code = GE; break;
        }
    }
}

void parse_declare_stmt() {
    assert(Let); assert(Id);
    l25_int *var_to_declare = symbol_ptr;

    if (var_to_declare[Class] == Loc && var_to_declare[Value] < 0) {
        printf("line %d: error: duplicate declaration of local variable '%s'.\n", line, (char *)var_to_declare[Name]);
        exit(-1);
    }
    var_to_declare[Class] = Loc;

    if (token == Colon) { // Struct variable: let obj : StructType
        assert(Colon); assert(Id);
        l25_int *type_sym = symbol_ptr;
        if (type_sym[Class] != Struct) {
            printf("line %d: error: unknown type '%s'.\n", line, (char *)type_sym[Name]);
            exit(-1);
        }
        var_to_declare[Type] = STRUCT_TYPE;
        var_to_declare[Extra] = (l25_int)(uintptr_t)type_sym;
        current_func_var_count += type_sym[Extra]; // Allocate stack space for all members.
    } else if (token == '[') { // Array declaration: let arr[size]
        assert('['); assert(Num);
        l25_int array_size = token_val;
        if (array_size <= 0 || array_size > MAX_ARRAY_SIZE) {  // 合理的数组大小限制
            printf("line %d: error: invalid array size %lld (must be 1-%d)\n", line, array_size, MAX_ARRAY_SIZE);
            exit(-1);
        }
        assert(']');
        var_to_declare[Type] = ARRAY_TYPE;
        var_to_declare[Extra] = INT_TYPE;
        current_func_var_count += array_size;
    } else { // Simple integer variable
        var_to_declare[Type] = INT_TYPE;
        current_func_var_count++;
    }
    var_to_declare[Value] = -current_func_var_count;

    if (token == Assign) {
        assert(Assign);
        check_code_overflow(3);
        *++code = LEA; *++code = var_to_declare[Value];
        *++code = PUSH;
        parse_expr();
        check_code_overflow(1);
        *++code = SI;
    }
}

void parse_assign_stmt_body(l25_int *id_var_entry) {
    if (token == '[') { // Array element assignment
        if (id_var_entry[Class] != Loc || id_var_entry[Type] != ARRAY_TYPE) {
             printf("line %d: error: '%s' is not an array.\n", line, (char *)id_var_entry[Name]); exit(-1);
        }
        assert('['); parse_expr(); assert(']'); assert(Assign);
        check_code_overflow(5);
        *++code = PUSH;
        *++code = LEA; *++code = id_var_entry[Value];
        *++code = LEA_ARRAY; *++code = PUSH;
        parse_expr();
        check_code_overflow(1); *++code = SI;
    } else if (token == Dot) { // Struct member assignment
        if (id_var_entry[Class] != Loc || id_var_entry[Type] != STRUCT_TYPE) {
             printf("line %d: error: '%s' is not a struct.\n", line, (char *)id_var_entry[Name]); exit(-1);
        }
        assert(Dot); assert(Id);
        int member_offset = find_struct_member((l25_int *)id_var_entry[Extra], symbol_ptr);
        if (member_offset < 0) {
            printf("line %d: error: struct member '%s' not found.\n", line, (char *)symbol_ptr[Name]); exit(-1);
        }
        assert(Assign);
        check_code_overflow(5);
        *++code = LEA; *++code = id_var_entry[Value];
        *++code = LEA_STRUCT; *++code = member_offset;
        *++code = PUSH;
        parse_expr();
        check_code_overflow(1); *++code = SI;
    } else if (token == Assign) { // Simple variable assignment
        assert(Assign);
        if (id_var_entry[Class] != Loc) {
             printf("line %d: error: cannot assign to non-variable '%s'.\n", line, (char *)id_var_entry[Name]); exit(-1);
        }
        check_code_overflow(3);
        *++code = LEA; *++code = id_var_entry[Value]; *++code = PUSH;
        parse_expr();
        check_code_overflow(1); *++code = SI;
    } else {
         printf("line %d: error: expected assignment operator after identifier.\n", line); exit(-1);
    }
}

void parse_func_call_stmt_body(l25_int *func_id_entry) {
    assert('(');
    int arg_count = 0;
    if (token != ')') {
        while (1) {
            parse_expr();
            check_code_overflow(1); *++code = PUSH;
            arg_count++;
            if (token == ',') assert(','); else break;
        }
    }
    assert(')');
    if (func_id_entry[Class] != Fun) {
        printf("line %d: error: '%s' is not a function.\n", line, (char *)func_id_entry[Name]); exit(-1);
    }
    check_code_overflow(4);
    *++code = CALL; *++code = func_id_entry[Value];
    if (arg_count > 0) {
        *++code = DARG; *++code = arg_count;
    }
}

void parse_if_stmt() {
    assert(If); assert('('); parse_expr(); assert(')');
    check_code_overflow(2);
    *++code = JZ; l25_int *patch_jz = ++code;
    assert('{'); parse_stmt_list(); assert('}');
    if (token == Else) {
        assert(Else);
        check_code_overflow(2);
        *++code = JMP; l25_int *patch_jmp_endif = ++code;
        *patch_jz = (l25_int)(uintptr_t)(code + 1);
        assert('{'); parse_stmt_list(); assert('}');
        *patch_jmp_endif = (l25_int)(uintptr_t)(code + 1);
    } else {
        *patch_jz = (l25_int)(uintptr_t)(code + 1);
    }
}

void parse_while_stmt() {
    assert(While);
    l25_int *loop_start = code + 1;
    assert('('); parse_expr(); assert(')');
    check_code_overflow(2);
    *++code = JZ; l25_int *patch_jz_end = ++code;
    assert('{'); parse_stmt_list(); assert('}');
    check_code_overflow(2);
    *++code = JMP; *++code = (l25_int)(uintptr_t)loop_start;
    *patch_jz_end = (l25_int)(uintptr_t)(code + 1);
}

void parse_input_stmt() {
    assert(Input); assert('(');
    int first = 1;
    while (1) {
        if (!first) assert(',');
        first = 0;
        assert(Id);
        if (symbol_ptr[Class] != Loc) {
            printf("line %d: error: input target '%s' must be a local variable.\n", line, (char *)symbol_ptr[Name]);
            exit(-1);
        }
        check_code_overflow(5);
        *++code = LEA; *++code = symbol_ptr[Value];
        *++code = PUSH; *++code = READ_INT_AX; *++code = SI;
        if (token != ',') break;
    }
    assert(')');
}

void parse_output_stmt() {
    assert(Output); assert('(');
    int first = 1;
    while (1) {
        if (!first) assert(',');
        first = 0;
        parse_expr();
        check_code_overflow(1);
        *++code = PRINT_INT_AX;
        if (token != ',') break;
    }
    assert(')');
}

void parse_stmt() {
    if (token == Let) parse_declare_stmt();
    else if (token == Id) {
        l25_int *id_entry = symbol_ptr;
        tokenize();
        if (token == Assign || token == '[' || token == Dot) parse_assign_stmt_body(id_entry);
        else if (token == '(') parse_func_call_stmt_body(id_entry);
        else {
            printf("line %d: error: unexpected token '%lld' after identifier.\n", line, token);
            exit(-1);
        }
    }
    else if (token == If) parse_if_stmt();
    else if (token == While) parse_while_stmt();
    else if (token == Input) parse_input_stmt();
    else if (token == Output) parse_output_stmt();
    else if (token == Return) {
        if (!current_parsing_func_body) {
            printf("line %d: error: 'return' not allowed outside function body.\n", line);
            exit(-1);
        }
        // Return语句应该由parse_stmt_list()或parse_func_def()处理，这里不应该出现
        printf("line %d: error: 'return' statement should be at the end of function body.\n", line);
        exit(-1);
    } else {
        printf("line %d: error: unknown statement starting with token %lld.\n", line, token);
        exit(-1);
    }
}

void parse_stmt_list() {
    int stmt_count = 0;
    while (token != '}' && !(current_parsing_func_body && token == Return) && token != 0) {
        l25_int stmt_start_token = token;
        parse_stmt();
        assert(';');
        stmt_count++;
    }

    if (stmt_count == 0 && !(current_parsing_func_body && token == Return)) {
        printf("line %d: error: statement list cannot be empty.\n", line);
        exit(-1);
    }
}

int parse_param_list() {
    int param_count = 0;
    while (token != ')') {
        if (param_count > 0) assert(',');
        assert(Id);
        if (param_count >= MAX_PARAMS) {
             printf("line %d: error: too many parameters (max %d).\n", line, MAX_PARAMS); exit(-1);
        }
        for (int k = 0; k < param_count; ++k) {
            if (param_sym_entries[k] == symbol_ptr) {
                 printf("line %d: error: duplicate parameter name '%s'.\n", line, (char *)symbol_ptr[Name]); exit(-1);
            }
        }
        param_sym_entries[param_count++] = symbol_ptr;
        if (token != ',') break;
    }
    for (int i = 0; i < param_count; ++i) {
        param_sym_entries[i][Class] = Loc;
        param_sym_entries[i][Type]  = INT_TYPE;
        param_sym_entries[i][Value] = (param_count - i) + 1;
    }
    return param_count;
}

void parse_func_def() {
    assert(FuncKw); assert(Id);
    if (symbol_ptr[Class] == Fun) {
        printf("line %d: error: duplicate function definition '%s'.\n", line, (char *)symbol_ptr[Name]); exit(-1);
    }
    symbol_ptr[Class] = Fun;
    symbol_ptr[Type] = INT_TYPE;
    symbol_ptr[Value] = (l25_int)(uintptr_t)(code + 1);

    current_func_var_count = 0;
    assert('(');
    if (token != ')') parse_param_list();
    assert(')');

    assert('{');
    current_parsing_func_body = 1;

    check_code_overflow(2);
    *++code = NVAR; l25_int *nvar_patch_addr = ++code;

    parse_stmt_list();
    *nvar_patch_addr = current_func_var_count;

    assert(Return);
    parse_expr();
    assert(';');
    check_code_overflow(1);
    *++code = RET;

    assert('}');
    current_parsing_func_body = 0;
}

void parse_struct_def() {
    assert(StructKw); assert(Id);
    if (symbol_ptr[Class] != 0) {
        printf("line %d: error: duplicate definition for '%s'.\n", line, (char *)symbol_ptr[Name]); exit(-1);
    }
    if (struct_count >= MAX_STRUCTS) {
        printf("line %d: error: too many struct definitions (max %d).\n", line, MAX_STRUCTS); exit(-1);
    }

    l25_int *struct_sym = symbol_ptr;
    struct_sym[Class] = Struct;
    struct_sym[Type]  = STRUCT_TYPE;
    struct_sym[Value] = struct_count;

    int current_struct_index = struct_count++;
    for (int i = 0; i < MAX_STRUCT_MEMBERS; ++i) struct_members[current_struct_index][i] = NULL;

    assert('{');
    int member_count = 0;
    while (token != '}' && token != 0) {
        assert(Id);
        if (member_count >= MAX_STRUCT_MEMBERS) {
            printf("line %d: error: too many members in struct (max %d).\n", line, MAX_STRUCT_MEMBERS); exit(-1);
        }
        for (int k = 0; k < member_count; ++k) {
            if (struct_members[current_struct_index][k] == symbol_ptr) {
                printf("line %d: error: duplicate member name '%s'.\n", line, (char *)symbol_ptr[Name]); exit(-1);
            }
        }
        // **FIX:** Store member symbol, but DO NOT modify its global entry.
        // The offset is implicitly its index in the members array.
        struct_members[current_struct_index][member_count++] = symbol_ptr;
        assert(';');
    }
    assert('}');
    if (member_count == 0) {
        printf("line %d: error: struct cannot be empty.\n", line); exit(-1);
    }
    struct_sym[Extra] = member_count;
}

int find_struct_member(l25_int *struct_def_sym, l25_int *member_sym) {
    if (!struct_def_sym || struct_def_sym[Class] != Struct) return -1;

    int struct_index = struct_def_sym[Value];
    int member_count = struct_def_sym[Extra];

    for (int i = 0; i < member_count; ++i) {
        // **FIX:** Compare symbol table pointers directly for uniqueness.
        if (struct_members[struct_index][i] == member_sym) {
            return i; // Return member offset (its index).
        }
    }
    return -1; // Not found.
}

void parse() {
    line = 1; token = 1;
    tokenize();

    assert(Program); assert(Id); assert('{');

    while (token == StructKw || token == FuncKw) {
        if (token == StructKw) parse_struct_def();
        else parse_func_def();
    }

    assert(MainKw);
    entry_main_addr = code + 1;
    current_func_var_count = 0;

    assert('{');
    current_parsing_func_body = 0;

    check_code_overflow(2);
    *++code = NVAR; l25_int *main_nvar_patch_addr = ++code;

    parse_stmt_list();
    *main_nvar_patch_addr = current_func_var_count;

    assert('}');
    assert('}');

    check_code_overflow(4);
    *++code = IMM; *++code = 0;
    *++code = PUSH; *++code = EXIT;

    if (token != 0) {
        printf("line %d: error: unexpected tokens after end of program.\n", line); exit(-1);
    }
}

/* ========== Keyword & VM Initialization ========== */
void keyword() {
    int i;
    char *keywords_str[] = {"program", "func", "main", "let", "if", "else", "return", "while", "input", "output", "struct", NULL};
    int keyword_tokens[] = {Program, FuncKw, MainKw, Let, If, Else, Return, While, Input, Output, StructKw};
    char *saved_src = src;
    for (i = 0; keywords_str[i]; ++i) {
        src = keywords_str[i];
        tokenize();
        symbol_ptr[Token] = keyword_tokens[i];
    }
    src = saved_src;
}

int init_vm() {
    if (!(code = code_dump = malloc(MAX_SIZE))) { printf("malloc failed for code segment\n"); return -1; }
    if (!(stack = malloc(MAX_SIZE))) { printf("malloc failed for stack segment\n"); return -1; }
    if (!(symbol_table = malloc(MAX_SIZE / 8))) { printf("malloc failed for symbol_table\n"); return -1; }

    memset(code, 0, MAX_SIZE);
    memset(stack, 0, MAX_SIZE);
    memset(symbol_table, 0, MAX_SIZE / 8);

    // Initialize boundary pointers for overflow checks.
    code_end = code + MAX_SIZE / sizeof(l25_int);
    stack_start = stack; // Stack grows downwards, so this is the "bottom".
    symbol_table_end = symbol_table + (MAX_SIZE / 8) / sizeof(l25_int);

    return 0;
}

/* ========== Virtual Machine Execution Engine ========== */
int run_vm(int argc_param, char **argv_param) {
    l25_int op;
    bp = sp = (l25_int *)((uintptr_t)stack + MAX_SIZE);
    if (!entry_main_addr) {
        printf("L25 main block entry point not defined.\n"); exit(-1);
    }
    pc = entry_main_addr;
    cycle = 0;

    while (1) {
        cycle++;
        // 防止无限循环攻击
        if (cycle > MAX_EXECUTION_CYCLES) {  // 防止无限循环
            printf("Runtime Error: Execution limit exceeded (possible infinite loop).\n");
            exit(-1);
        }
        op = *pc++;

        if (op == IMM) { ax = *pc++; }
        else if (op == LEA) { ax = (l25_int)(uintptr_t)(bp + *pc++); }
        else if (op == JMP) {
            l25_int *target_addr = (l25_int *)(uintptr_t)*pc;
            if (!is_valid_pc_address(target_addr)) {
                printf("Runtime Error: Invalid jump address in JMP.\n");
                exit(-1);
            }
            pc = target_addr;
        }
        else if (op == JZ) {
            if (ax) {
                pc = pc + 1;
            } else {
                l25_int *target_addr = (l25_int *)(uintptr_t)*pc;
                if (target_addr < code_dump || target_addr >= code_end) {
                    printf("Runtime Error: Invalid jump address in JZ.\n");
                    exit(-1);
                }
                pc = target_addr;
            }
        }
        else if (op == JNZ) {
            if (ax) {
                l25_int *target_addr = (l25_int *)(uintptr_t)*pc;
                if (target_addr < code_dump || target_addr >= code_end) {
                    printf("Runtime Error: Invalid jump address in JNZ.\n");
                    exit(-1);
                }
                pc = target_addr;
            } else {
                pc = pc + 1;
            }
        }
        else if (op == CALL) {
            // 先检查栈溢出，再执行操作
            if (sp - 1 < stack_start) {
                printf("Runtime Error: Stack overflow on CALL.\n");
                exit(-1);
            }
            // 验证跳转地址的有效性
            l25_int *target_addr = (l25_int *)(uintptr_t)*pc;
            if (!is_valid_pc_address(target_addr)) {
                printf("Runtime Error: Invalid function address in CALL.\n");
                exit(-1);
            }
            *--sp = (l25_int)(uintptr_t)(pc + 1);
            pc = target_addr;
        }
        else if (op == NVAR) {
            *--sp = (l25_int)(uintptr_t)bp; bp = sp;
            if (sp - *pc < stack_start) { printf("Runtime Error: Stack overflow on NVAR.\n"); exit(-1); }
            sp = sp - *pc++;
        }
        else if (op == DARG) { sp = sp + *pc++; }
        else if (op == RET) { sp = bp; bp = (l25_int *)(uintptr_t)*sp++; pc = (l25_int *)(uintptr_t)*sp++; }
        else if (op == LI) {
            // 验证内存访问地址的有效性
            l25_int *addr = (l25_int *)(uintptr_t)ax;
            if (addr < stack_start || addr >= (l25_int *)((uintptr_t)stack + MAX_SIZE)) {
                printf("Runtime Error: Invalid memory access in LI at address %p.\n", (void*)addr);
                exit(-1);
            }
            ax = *addr;
        }
        else if (op == SI) {
            // 验证内存写入地址的有效性
            l25_int *addr = (l25_int *)(uintptr_t)*sp++;
            if (addr < stack_start || addr >= (l25_int *)((uintptr_t)stack + MAX_SIZE)) {
                printf("Runtime Error: Invalid memory write in SI at address %p.\n", (void*)addr);
                exit(-1);
            }
            *addr = ax;
        }
        else if (op == PUSH) {
            if (sp - 1 < stack_start) { printf("Runtime Error: Stack overflow on PUSH.\n"); exit(-1); }
            *--sp = ax;
        }
        else if (op == ADD) { ax = *sp++ + ax; }
        else if (op == SUB) { ax = *sp++ - ax; }
        else if (op == MUL) { ax = *sp++ * ax; }
        else if (op == DIV) {
            if (ax == 0) {
                printf("Runtime Error: Division by zero.\n");
                exit(-2);
            }
            // 检查整数溢出：INT64_MIN / -1 会溢出
            l25_int dividend = *sp++;
            if (dividend == INT64_MIN && ax == -1) {
                printf("Runtime Error: Division overflow (INT64_MIN / -1).\n");
                exit(-2);
            }
            ax = dividend / ax;
        }
        else if (op == EQ) { ax = (*sp++ == ax); }
        else if (op == NE) { ax = (*sp++ != ax); }
        else if (op == LT) { ax = (*sp++ < ax); }
        else if (op == GT) { ax = (*sp++ > ax); }
        else if (op == LE) { ax = (*sp++ <= ax); }
        else if (op == GE) { ax = (*sp++ >= ax); }
        else if (op == EXIT) { printf("L25 program exited with code: %lld (cycles: %lld)\n", *sp, cycle); return *sp; }
        else if (op == READ_INT_AX) { fflush(stdout); scanf("%lld", &ax); }
        else if (op == PRINT_INT_AX) { printf("%lld\n", ax); fflush(stdout); }
        else if (op == LEA_ARRAY) { ax = (l25_int)((uintptr_t)ax + (*sp++) * sizeof(l25_int)); }
        else if (op == LEA_STRUCT) { ax = (l25_int)((uintptr_t)ax + (*pc++) * sizeof(l25_int)); }
        else { printf("Runtime Error: Unknown instruction %lld (cycle: %lld)\n", op, cycle); return -1; }
    }
    return 0;
}

/* ========== Memory Cleanup ========== */
void cleanup_vm() {
    if (code_dump) { free(code_dump); code_dump = code = NULL; }
    if (stack) { free(stack); stack = NULL; }
    if (symbol_table) { free(symbol_table); symbol_table = NULL; }
    if (src_dump) { free(src_dump); src_dump = src = NULL; }
}

/* ========== Debug & Main ========== */
void write_as() {
    int fd;
    char buffer[100];
    char *insts = "IMM ,LEA ,JMP ,JZ  ,JNZ ,CALL,NVAR,DARG,RET ,LI  ,SI  ,PUSH,ADD ,SUB ,MUL ,DIV ,EQ  ,NE  ,LT  ,GT  ,LE  ,GE  ,EXIT,RDAX,PTAX,LEAR,LEAS,";
    if ((fd = open("assemble.l25", O_WRONLY | O_CREAT | O_TRUNC, 0644)) < 0) { printf("Failed to open assemble.l25.\n"); return; }

    l25_int *ptr = code_dump;
    int addr = 0;
    while(ptr <= code) {
        l25_int op = *ptr;
        char mnemonic[6];
        if (op >= 0 && (op * 5 + 4) < strlen(insts)) {
            strncpy(mnemonic, insts + (op * 5), 4);
            mnemonic[4] = '\0';
        } else {
            strcpy(mnemonic, "UNKN");
        }
        snprintf(buffer, sizeof(buffer), "%04d: %-5s", addr, mnemonic);
        write(fd, buffer, strlen(buffer));

        ptr++; addr++;
        if (op == IMM || op == LEA || op == JZ || op == JNZ || op == CALL || op == NVAR || op == DARG || op == JMP || op == LEA_STRUCT) {
            snprintf(buffer, sizeof(buffer), " %lld\n", *ptr);
            ptr++; addr++;
        } else {
            strcpy(buffer, "\n");
        }
        write(fd, buffer, strlen(buffer));
    }
    close(fd);
    printf("Assembly-like output written to assemble.l25\n");
}

int load_src(char *file) {
    int fd;
    int cnt;
    if ((fd = open(file, O_RDONLY)) < 0) {
        printf("Error: Could not open source file '%s'\n", file); return -1;
    }
    if (!(src = src_dump = malloc(MAX_SIZE))) {
        printf("Error: Could not malloc for source code\n"); close(fd); return -1;
    }
    if ((cnt = read(fd, src, MAX_SIZE - 1)) <= 0) {
        printf("Error: Could not read source or file is empty\n"); free(src_dump); close(fd); return -1;
    }
    if (cnt >= MAX_SIZE - 1) {
        printf("Error: Source file too large (max %d bytes)\n", MAX_SIZE - 1);
        free(src_dump); close(fd); return -1;
    }
    src[cnt] = 0;
    close(fd);
    return 0;
}

int main(int argc, char *argv[]) {
    int result = 0;

    if (argc < 2) {
        printf("Usage: %s <sourcefile.l25>\n", argv[0]); return -1;
    }

    MAX_SIZE = 256 * 1024 * 8; // 2MB

    if (load_src(argv[1]) != 0) { cleanup_vm(); return -1; }
    if (init_vm() != 0) { cleanup_vm(); return -1; }
    keyword();
    code++; // Start code generation after the first address to avoid address 0.
    parse();
    write_as();

    result = run_vm(argc - 1, argv + 1);
    cleanup_vm();
    return result;
}