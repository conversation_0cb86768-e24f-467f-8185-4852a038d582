program test {
    func add(a, b) {
        let result = a + b;
        return result;
    }
    
    func factorial(n) {
        if (n <= 1) {
            return 1;
        } else {
            return n * factorial(n - 1);
        }
    }
    
    main {
        let x = 5;
        let y = 3;
        let sum = add(x, y);
        output(sum);
        
        let n = 5;
        let fact = factorial(n);
        output(fact);
        
        let i = 1;
        while (i <= 3) {
            output(i);
            i = i + 1;
        }
    }
}
