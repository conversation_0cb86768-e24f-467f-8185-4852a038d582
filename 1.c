/*
 * L25.c - 一个简单的编程语言编译器和虚拟机
 *
 * 这个文件实现了一个完整的编程语言处理系统，包括：
 * 1. 词法分析器（Lexer）- 将源代码转换为标记（Token）
 * 2. 语法分析器（Parser）- 将标记转换为虚拟机指令
 * 3. 虚拟机（VM）- 执行生成的指令
 *
 * L25语言支持：
 * - 基本数据类型：整数
 * - 控制结构：if/else、while循环
 * - 函数定义和调用
 * - 一维静态数组
 * - 结构体定义和使用
 * - 输入输出操作
 */

#include <fcntl.h>    // 文件操作相关函数
#include <memory.h>   // 内存操作函数
#include <stdint.h>   // 标准整数类型定义
#include <stdio.h>    // 标准输入输出函数
#include <stdlib.h>   // 标准库函数（malloc、exit等）
#include <string.h>   // 字符串操作函数
#include <unistd.h>   // Unix标准函数

#define int int64_t // 全局定义：将int重定义为64位整数，确保数据一致性

/* ========== 全局变量定义 ========== */

int MAX_SIZE; // 内存段的最大大小

/* 虚拟机的内存段 */
int *code,      // 代码段：存储编译后的虚拟机指令
    *code_dump, // 代码段备份：用于调试输出
    *stack;     // 栈段：程序运行时的数据栈

/* 虚拟机的寄存器 */
int *pc, // 程序计数器（Program Counter）：指向当前执行的指令
    *sp, // 栈指针（Stack Pointer）：指向栈顶
    *bp; // 基址指针（Base Pointer）：指向当前函数的栈帧基址

int ax,    // 通用寄存器：用于存储计算结果和临时数据
    cycle; // 指令执行计数器：用于调试和性能分析

/* 词法分析器和语法分析器的全局状态 */
char *src,      // 源代码指针：指向当前正在分析的字符
     *src_dump; // 源代码备份：保存原始源代码起始地址
int *symbol_table, // 符号表：存储所有标识符（变量名、函数名等）的信息
    *symbol_ptr;   // 符号表指针：指向当前正在处理的符号表项
int token,         // 当前标记类型：表示当前识别出的语法元素类型
    token_val;     // 当前标记值：如果是数字，存储其数值
int line;          // 当前行号：用于错误报告

/* L25语言特定的解析和虚拟机状态 */
int *entry_main_addr = 0;          // main函数入口地址：程序开始执行的位置
int current_func_var_count = 0;    // 当前函数中的局部变量数量：用于栈空间分配
int current_parsing_func_body = 0; // 函数体解析标志：1表示正在解析函数体，0表示其他位置

/* 函数参数解析相关 */
#define MAX_PARAMS 10                     // 函数最大参数数量限制
int *param_sym_entries[MAX_PARAMS];       // 参数符号表项数组：存储函数参数的符号信息

/* 结构体定义相关 */
#define MAX_STRUCT_MEMBERS 20                                           // 结构体最大成员数量限制
#define MAX_STRUCTS 10                                                  // 最大结构体定义数量限制
int *struct_members[MAX_STRUCTS][MAX_STRUCT_MEMBERS];                   // 二维数组：存储每个结构体的成员信息
int struct_count = 0;                                                   // 已定义的结构体数量计数器
int *current_struct_def = 0;                                            // 当前正在定义的结构体指针

/* ========== 枚举定义 ========== */

/*
 * 虚拟机指令集
 * 这些指令定义了L25虚拟机能够执行的所有操作
 * 每个指令都有特定的功能，用于实现程序的各种行为
 */
enum
{
    /* 基本数据操作指令 */
    IMM,  // 立即数加载：将一个常数值加载到ax寄存器中
    LEA,  // 加载有效地址：计算局部变量或参数相对于bp的地址，存入ax
    JMP,  // 无条件跳转：直接跳转到指定地址
    JZ,   // 零跳转：如果ax为0则跳转，否则继续执行
    JNZ,  // 非零跳转：如果ax不为0则跳转，否则继续执行

    /* 函数调用相关指令 */
    CALL, // 函数调用：保存返回地址，跳转到函数入口
    NVAR, // 新建变量：在栈上为局部变量分配空间
    DARG, // 丢弃参数：函数调用后清理栈上的参数
    RET,  // 函数返回：恢复调用者的栈帧，返回到调用点

    /* 内存访问指令 */
    LI, // 加载整数：从ax指向的内存地址加载数据到ax
    SI, // 存储整数：将ax中的值存储到栈顶地址指向的内存位置

    /* 栈操作指令 */
    PUSH, // 压栈：将ax的值压入栈顶

    /* 算术运算指令（左操作数来自栈，右操作数是ax，结果存入ax） */
    ADD, // 加法：栈顶值 + ax
    SUB, // 减法：栈顶值 - ax
    MUL, // 乘法：栈顶值 * ax
    DIV, // 除法：栈顶值 / ax

    /* 比较运算指令（左操作数来自栈，右操作数是ax，结果存入ax：1表示真，0表示假） */
    EQ, // 等于：栈顶值 == ax
    NE, // 不等于：栈顶值 != ax
    LT, // 小于：栈顶值 < ax
    GT, // 大于：栈顶值 > ax
    LE, // 小于等于：栈顶值 <= ax
    GE, // 大于等于：栈顶值 >= ax

    /* 程序控制指令 */
    EXIT, // 程序退出：使用栈顶值作为退出码终止程序

    /* L25语言的输入输出指令 */
    READ_INT_AX,  // 读取整数：从标准输入读取一个整数到ax
    PRINT_INT_AX, // 打印整数：将ax中的整数输出到标准输出

    /* 数组和结构体访问指令（新增功能） */
    LEA_ARRAY,   // 数组元素地址计算：ax = 基址 + 索引 * 元素大小
    LEA_STRUCT   // 结构体成员地址计算：ax = 基址 + 成员偏移量
};

/*
 * L25语言的标记类型（Token Types）
 * 词法分析器将源代码分解为这些标记，语法分析器根据标记类型进行语法分析
 */
enum
{
    /* 基本标记类型（从128开始，避免与ASCII字符冲突） */
    Num = 128,  // 数字常量：如 123、456
    Fun,        // 函数类型：标识符被识别为函数
    Loc,        // 局部变量类型：标识符被识别为变量
    Struct,     // 结构体类型：标识符被识别为结构体
    Array,      // 数组类型：标识符被识别为数组
    Id,         // 标识符：变量名、函数名等

    /* L25语言的关键字 */
    Program,   // program关键字：程序开始标志
    FuncKw,    // func关键字：函数定义
    MainKw,    // main关键字：主函数
    Let,       // let关键字：变量声明
    If,        // if关键字：条件语句
    Else,      // else关键字：条件语句的else分支
    Return,    // return关键字：函数返回
    While,     // while关键字：循环语句
    Input,     // input关键字：输入操作
    Output,    // output关键字：输出操作
    StructKw,  // struct关键字：结构体定义

    /* L25语言的操作符（需要特殊处理的操作符） */
    Assign,    // 赋值操作符：=

    /* 算术和比较操作符 */
    Add,       // 加法操作符：+
    Sub,       // 减法操作符：-
    Mul,       // 乘法操作符：*
    Div,       // 除法操作符：/
    Eq,        // 等于操作符：==
    Ne,        // 不等于操作符：!=
    Lt,        // 小于操作符：<
    Gt,        // 大于操作符：>
    Le,        // 小于等于操作符：<=
    Ge,        // 大于等于操作符：>=

    /* 数组和结构体相关的新操作符 */
    Dot,       // 点操作符：. 用于结构体成员访问
    Colon      // 冒号操作符：: 用于类型注解
};

/*
 * 符号表条目字段定义
 * 符号表用于存储程序中所有标识符的信息（变量、函数、结构体等）
 * 每个符号表条目包含以下字段：
 */
enum
{
    Token,  // 标记类型：如果是关键字则存储关键字类型，否则为Id
    Hash,   // 哈希值：标识符字符串的哈希值，用于快速查找
    Name,   // 名称指针：指向源代码中标识符字符串的指针
    Class,  // 类别：Fun（函数）、Loc（局部变量）、Struct（结构体）、Array（数组）
    Type,   // 数据类型：INT_TYPE、STRUCT_TYPE、ARRAY_TYPE
    Value,  // 值字段：根据类别有不同含义
            //   - Fun: 函数代码地址
            //   - Loc: 相对于bp的栈偏移量
            //   - Struct: 结构体定义索引
            //   - Array: 数组大小
    Extra,  // 扩展字段：
            //   - 结构体: 成员数量
            //   - 数组: 元素类型
    SymSize // 符号表条目的字段数量
};

/*
 * L25语言支持的数据类型
 */
enum
{
    INT_TYPE,    // 整数类型：L25的基本数字类型
    STRUCT_TYPE, // 结构体类型：用户定义的复合类型
    ARRAY_TYPE   // 数组类型：一维静态数组
};

/* ========== 词法分析器（Lexical Analyzer / Tokenizer） ========== */

/*
 * 词法分析器主函数
 * 功能：将源代码字符流转换为标记（Token）流
 *
 * 工作原理：
 * 1. 逐个读取源代码字符
 * 2. 根据字符类型识别不同的语法元素
 * 3. 将识别出的元素转换为对应的标记类型
 * 4. 对于标识符，在符号表中查找或添加新条目
 *
 * 无参数，无返回值
 * 结果存储在全局变量token和token_val中
 */
void tokenize()
{
    char *ch_ptr;         // 指向当前标识符开始位置的指针
    int current_hash_val; // 当前标识符的哈希值累加器

    // 主循环：逐个处理源代码字符
    while ((token = *src++))
    {
        // 处理换行符：增加行号计数，用于错误报告
        if (token == '\n')
        {
            line++;
        }
        // 跳过空白字符：空格、制表符、回车符
        else if (token == ' ' || token == '\t' || token == '\r')
        {
            // 什么都不做，继续下一个字符
        }
        // 处理标识符：以字母或下划线开头，后面可以跟字母、数字、下划线
        else if ((token >= 'a' && token <= 'z') || (token >= 'A' && token <= 'Z') || (token == '_'))
        {
            ch_ptr = src - 1;         // 记录标识符的起始位置
            current_hash_val = token; // 用第一个字符初始化哈希值

            // 继续读取标识符的剩余部分
            while ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z') || (*src >= '0' && *src <= '9') ||
                   (*src == '_'))
            {
                current_hash_val = current_hash_val * 147 + *src++; // 计算哈希值
            }
            current_hash_val = (current_hash_val << 6) + (src - ch_ptr); // 最终哈希值

            // 在符号表中查找该标识符
            symbol_ptr = symbol_table;
            while (symbol_ptr[Token])
            {
                // 比较哈希值和字符串内容
                if (current_hash_val == symbol_ptr[Hash] && !memcmp((char *)symbol_ptr[Name], ch_ptr, src - ch_ptr))
                {
                    token = symbol_ptr[Token]; // 找到了，使用已有的标记类型
                    return;
                }
                symbol_ptr = symbol_ptr + SymSize; // 移动到下一个符号表条目
            }
            // 标识符未找到，添加新的标识符到符号表
            symbol_ptr[Name] = (int64_t)ch_ptr;        // 存储标识符字符串的地址
            symbol_ptr[Hash] = current_hash_val;       // 存储哈希值
            token = symbol_ptr[Token] = Id;            // 设置为标识符类型
            return;
        }
        // 处理数字常量：以数字开头的字符序列
        else if (token >= '0' && token <= '9')
        {
            token_val = token - '0'; // 将字符转换为数字
            // 继续读取数字的剩余部分
            while (*src >= '0' && *src <= '9')
            {
                token_val = token_val * 10 + *src++ - '0'; // 构建完整的数字
            }
            token = Num; // 设置标记类型为数字
            return;
        }
        // 处理除法操作符和注释
        else if (token == '/')
        {
            if (*src == '/') // 双斜杠表示行注释
            {
                // 跳过整行注释内容
                while (*src != 0 && *src != '\n')
                    src++;
            }
            else
            {
                token = Div; // 单个斜杠是除法操作符
                return;
            }
        }
        // 处理等号相关操作符
        else if (token == '=')
        {
            if (*src == '=') // == 等于操作符
            {
                src++;
                token = Eq;
            }
            else // = 赋值操作符
            {
                token = Assign;
            }
            return;
        }
        // 处理感叹号相关操作符
        else if (token == '!')
        {
            if (*src == '=') // != 不等于操作符
            {
                src++;
                token = Ne;
            }
            else
            {
                // 单独的'!'在L25中不是有效操作符，返回ASCII值让语法分析器处理错误
            }
            return;
        }
        // 处理小于号相关操作符
        else if (token == '<')
        {
            if (*src == '=') // <= 小于等于操作符
            {
                src++;
                token = Le;
            }
            else // < 小于操作符
            {
                token = Lt;
            }
            return;
        }
        // 处理大于号相关操作符
        else if (token == '>')
        {
            if (*src == '=') // >= 大于等于操作符
            {
                src++;
                token = Ge;
            }
            else // > 大于操作符
            {
                token = Gt;
            }
            return;
        }
        // 处理算术操作符
        else if (token == '+')
        {
            token = Add; // 加法操作符
            return;
        }
        else if (token == '-')
        {
            token = Sub; // 减法操作符
            return;
        }
        else if (token == '*')
        {
            token = Mul; // 乘法操作符
            return;
        }
        // 处理结构体和数组相关的新操作符
        else if (token == '.')
        {
            token = Dot; // 点操作符，用于结构体成员访问
            return;
        }
        else if (token == ':')
        {
            token = Colon; // 冒号操作符，用于类型注解
            return;
        }
        // 处理单字符标点符号：括号、大括号、方括号、逗号、分号
        else if (token == '(' || token == ')' || token == '{' || token == '}' || token == ',' || token == ';' ||
                 token == '[' || token == ']')
        {
            return; // 直接返回ASCII值，语法分析器会识别这些字符
        }
        // 隐式跳过未知字符，继续循环处理下一个字符
    }
}

/* ========== 语法分析器辅助函数 ========== */

/*
 * 断言函数：检查当前标记是否符合期望
 * 参数：tk - 期望的标记类型
 * 功能：
 * 1. 检查当前标记是否与期望的标记匹配
 * 2. 如果不匹配，输出详细的错误信息并退出程序
 * 3. 如果匹配，读取下一个标记
 *
 * 这是语法分析器的核心辅助函数，确保语法的正确性
 */
void assert(int tk) // tk是int64_t类型（由于全局#define）
{
    if (token != tk)
    {
        char expected_char_printable = 0, current_char_printable = 0;

        // 检查标记是否在可打印ASCII范围内，以便友好显示
        if (tk > 31 && tk < 127)
            expected_char_printable = (char)tk;
        if (token > 31 && token < 127)
            current_char_printable = (char)token;

        // 根据标记类型输出不同格式的错误信息
        if (expected_char_printable && current_char_printable)
            printf("line %ld: error: expected token %ld ('%c'), but got %ld ('%c')\n", line, tk,
                   expected_char_printable, token, current_char_printable);
        else if (expected_char_printable)
            printf("line %ld: error: expected token %ld ('%c'), but got %ld\n", line, tk, expected_char_printable,
                   token);
        else if (current_char_printable)
            printf("line %ld: error: expected token %ld, but got %ld ('%c')\n", line, tk, token,
                   current_char_printable);
        else
            printf("line %ld: error: expected token %ld, but got %ld\n", line, tk, token);
        exit(-1); // 语法错误，退出程序
    }
    tokenize(); // 读取下一个标记
}

/* ========== L25语法分析器函数 ========== */

// 函数声明：这些函数相互递归调用，实现语法分析
void parse_expr();                                                    // 解析表达式
void parse_stmt();                                                    // 解析语句
void parse_stmt_list();                                               // 解析语句列表
void parse_struct_def();                                              // 解析结构体定义
int find_struct_member(int *struct_sym, char *member_name);           // 查找结构体成员

/*
 * 解析因子（Factor）
 * 功能：解析表达式中的最基本单元
 *
 * 因子可以是：
 * 1. 标识符（变量、函数调用、数组访问、结构体成员访问）
 * 2. 数字常量
 * 3. 括号表达式
 *
 * 这个函数处理表达式解析的最底层，是递归下降解析器的基础
 */
void parse_factor()
{
    int *id_sym_entry; // 指向标识符符号表条目的指针

    if (token == Id) // 处理标识符
    {
        id_sym_entry = symbol_ptr; // 保存当前标识符的符号表条目
        tokenize(); // 读取下一个标记

        if (token == '(') // 函数调用：identifier(arguments)
        {
            assert('(');
            int arg_count = 0; // 参数计数器

            // 解析参数列表
            if (token != ')')
            {
                while (1)
                {
                    parse_expr();       // 解析参数表达式
                    *++code = PUSH;     // 将参数值压栈
                    arg_count++;
                    if (token == ',')
                        assert(',');    // 处理参数分隔符
                    else
                        break;          // 参数列表结束
                }
            }
            assert(')');

            // 检查标识符是否为函数
            if (id_sym_entry[Class] != Fun)
            {
                printf("line %ld: error: '%s' is not a function.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }

            // 生成函数调用指令
            *++code = CALL;                    // 调用指令
            *++code = id_sym_entry[Value];     // 函数地址
            if (arg_count > 0)
            {
                *++code = DARG;                // 清理参数指令
                *++code = arg_count;           // 参数数量
            }
        }
        else if (token == '[') // 数组访问：identifier[index]
        {
            // 检查标识符是否为数组类型
            if (id_sym_entry[Class] != Loc || id_sym_entry[Type] != ARRAY_TYPE)
            {
                printf("line %ld: error: '%s' is not an array.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            assert('[');
            parse_expr();                      // 解析索引表达式，结果存入ax
            assert(']');

            // 生成数组元素访问指令
            *++code = PUSH;                    // 将索引值压栈
            *++code = LEA;                     // 加载数组基址
            *++code = id_sym_entry[Value];     // 数组在栈中的偏移量
            *++code = LEA_ARRAY;               // 计算元素地址：基址 + 索引 * 元素大小
            *++code = LI;                      // 从计算出的地址加载值
        }
        else if (token == Dot) // 结构体成员访问：identifier.member
        {
            // 检查标识符是否为结构体类型
            if (id_sym_entry[Class] != Loc || id_sym_entry[Type] != STRUCT_TYPE)
            {
                printf("line %ld: error: '%s' is not a struct.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            assert(Dot);
            assert(Id);

            // 查找结构体成员的偏移量
            int member_offset = find_struct_member(id_sym_entry, (char *)symbol_ptr[Name]);
            if (member_offset < 0)
            {
                printf("line %ld: error: struct member '%s' not found.\n", line, (char *)symbol_ptr[Name]);
                exit(-1);
            }

            // 生成结构体成员访问指令
            *++code = LEA;                     // 加载结构体基址
            *++code = id_sym_entry[Value];     // 结构体在栈中的偏移量
            *++code = LEA_STRUCT;              // 计算成员地址：基址 + 成员偏移量
            *++code = member_offset;           // 成员偏移量作为操作数
            *++code = LI;                      // 从计算出的地址加载值
        }
        else // 简单变量访问：identifier
        {
            // 检查标识符是否为变量
            if (id_sym_entry[Class] != Loc)
            {
                printf("line %ld: error: undefined variable '%s'.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }

            // 生成变量访问指令
            *++code = LEA;                     // 加载变量地址
            *++code = id_sym_entry[Value];     // 变量在栈中的偏移量
            *++code = LI;                      // 从地址加载值
        }
    }
    else if (token == Num) // 数字常量
    {
        // 生成立即数加载指令
        *++code = IMM;        // 立即数指令
        *++code = token_val;  // 数字值
        tokenize();
    }
    else if (token == '(') // 括号表达式：(expression)
    {
        assert('(');
        parse_expr();         // 递归解析括号内的表达式
        assert(')');
    }
    else
    {
        printf("line %ld: error: unexpected token in factor, got %ld.\n", line, token);
        exit(-1);
    }
}

/*
 * 解析项（Term）
 * 功能：处理乘法和除法运算
 *
 * 项的语法：factor { ('*' | '/') factor }
 * 例如：a * b / c
 *
 * 运算优先级：乘除法比加减法优先级高
 */
void parse_term()
{
    parse_factor(); // 解析第一个因子

    // 处理连续的乘除运算
    while (token == Mul || token == Div)
    {
        int op = token; // 保存操作符类型
        tokenize();
        *++code = PUSH;     // 将左操作数压栈
        parse_factor();     // 解析右操作数

        // 生成对应的运算指令
        if (op == Mul)
            *++code = MUL;  // 乘法指令
        else
            *++code = DIV;  // 除法指令
    }
}

/*
 * 解析加减表达式（Sum Expression）
 * 功能：处理加法、减法运算和一元正负号
 *
 * 加减表达式的语法：['+' | '-'] term { ('+' | '-') term }
 * 例如：-a + b - c
 *
 * 运算优先级：加减法比比较运算优先级高，比乘除法优先级低
 */
void parse_sum_expr()
{
    int unary_op = 0; // 一元操作符标志：0=无，1=正号，2=负号

    // 处理一元正负号
    if (token == Add)
    {
        unary_op = 1; // 一元正号（实际上不需要特殊处理）
        tokenize();
    }
    else if (token == Sub)
    {
        unary_op = 2; // 一元负号
        tokenize();
    }

    parse_term(); // 解析第一个项

    // 如果有一元负号，生成取负指令
    if (unary_op == 2)
    {
        *++code = PUSH;  // 将值压栈
        *++code = IMM;   // 加载立即数-1
        *++code = -1;
        *++code = MUL;   // 乘以-1实现取负
    }

    // 处理连续的加减运算
    while (token == Add || token == Sub)
    {
        int op = token; // 保存操作符类型
        tokenize();
        *++code = PUSH;     // 将左操作数压栈
        parse_term();       // 解析右操作数

        // 生成对应的运算指令
        if (op == Add)
            *++code = ADD;  // 加法指令
        else
            *++code = SUB;  // 减法指令
    }
}

/*
 * 解析表达式（Expression）
 * 功能：处理比较运算，这是表达式解析的最高层
 *
 * 表达式的语法：sum_expr [('==' | '!=' | '<' | '<=' | '>' | '>=') sum_expr]
 * 例如：a + b > c * d
 *
 * 运算优先级：比较运算优先级最低
 */
void parse_expr()
{
    parse_sum_expr(); // 解析左操作数

    // 处理比较运算符
    if (token == Eq || token == Ne || token == Lt || token == Le || token == Gt || token == Ge)
    {
        int op = token; // 保存比较操作符类型
        tokenize();
        *++code = PUSH;     // 将左操作数压栈
        parse_sum_expr();   // 解析右操作数

        // 生成对应的比较指令
        if (op == Eq)
            *++code = EQ;       // 等于比较
        else if (op == Ne)
            *++code = NE;       // 不等于比较
        else if (op == Lt)
            *++code = LT;       // 小于比较
        else if (op == Le)
            *++code = LE;       // 小于等于比较
        else if (op == Gt)
            *++code = GT;       // 大于比较
        else if (op == Ge)
            *++code = GE;       // 大于等于比较
    }
}

/*
 * 解析变量声明语句
 * 功能：处理let语句，支持普通变量、数组和结构体的声明
 *
 * 声明语法：
 * - let var;              // 普通整数变量
 * - let var = expr;       // 带初始化的变量
 * - let arr[size];        // 数组声明
 * - let obj : StructType; // 结构体变量声明
 */
void parse_declare_stmt()
{
    assert(Let);
    assert(Id);
    int *var_to_declare = symbol_ptr; // 获取要声明的变量的符号表条目

    // 检查是否重复声明局部变量（局部变量的Value为负偏移量）
    if (var_to_declare[Class] == Loc && var_to_declare[Value] < 0)
    {
        printf("line %ld: error: duplicate declaration of local variable '%s'.\n", line, (char *)var_to_declare[Name]);
        exit(-1);
    }

    var_to_declare[Class] = Loc; // 标记为局部变量

    // 检查类型注解：let var : Type
    if (token == Colon)
    {
        assert(Colon);
        assert(Id);

        // 在符号表中查找类型定义
        int *type_sym = symbol_ptr;
        if (type_sym[Class] == Struct)
        {
            // 结构体类型变量
            var_to_declare[Type] = STRUCT_TYPE;
            var_to_declare[Extra] = (int64_t)type_sym;     // 存储指向结构体定义的指针
            current_func_var_count += type_sym[Extra];     // 为所有结构体成员分配栈空间
        }
        else
        {
            printf("line %ld: error: unknown type '%s'.\n", line, (char *)type_sym[Name]);
            exit(-1);
        }
    }
    else if (token == '[') // 数组声明：let arr[size]
    {
        assert('[');
        assert(Num);
        int array_size = token_val; // 获取数组大小
        assert(']');

        var_to_declare[Type] = ARRAY_TYPE;
        var_to_declare[Extra] = INT_TYPE;              // 元素类型（目前只支持整数）
        current_func_var_count += array_size;         // 为所有数组元素分配栈空间
    }
    else
    {
        // 普通整数变量
        var_to_declare[Type] = INT_TYPE;
        current_func_var_count++;
    }

    // 设置变量在栈中的偏移量（负值表示局部变量）
    var_to_declare[Value] = -current_func_var_count;

    // 处理初始化：let var = expr
    if (token == Assign)
    {
        assert(Assign);

        // 生成初始化代码
        *++code = LEA;                       // 加载变量地址
        *++code = var_to_declare[Value];     // 变量的栈偏移量
        *++code = PUSH;                      // 将地址压栈
        parse_expr();                        // 解析初始化表达式，结果存入ax
        *++code = SI;                        // 将ax中的值存储到栈顶地址指向的位置
    }
}

/*
 * 解析赋值语句体（支持数组和结构体赋值）
 * 功能：处理各种形式的赋值操作
 *
 * 参数：id_var_entry - 要赋值的变量的符号表条目
 *
 * 支持的赋值形式：
 * - var = expr;           // 普通变量赋值
 * - arr[index] = expr;    // 数组元素赋值
 * - struct.member = expr; // 结构体成员赋值
 */
void parse_assign_stmt_body(int *id_var_entry)
{
    // 处理数组元素赋值：arr[index] = expr
    if (token == '[')
    {
        // 检查标识符是否为数组类型
        if (id_var_entry[Class] != Loc || id_var_entry[Type] != ARRAY_TYPE)
        {
            printf("line %ld: error: '%s' is not an array.\n", line, (char *)id_var_entry[Name]);
            exit(-1);
        }
        assert('[');
        parse_expr();                      // 解析索引表达式，结果存入ax
        assert(']');
        assert(Assign);

        // 生成数组元素赋值指令
        *++code = PUSH;                    // 将索引值压栈
        *++code = LEA;                     // 加载数组基址
        *++code = id_var_entry[Value];     // 数组在栈中的偏移量
        *++code = LEA_ARRAY;               // 计算元素地址
        *++code = PUSH;                    // 将元素地址压栈
        parse_expr();                      // 解析右侧表达式，结果存入ax
        *++code = SI;                      // 将ax中的值存储到栈顶地址指向的位置
    }
    // Handle struct member assignment: struct.member = expr
    else if (token == Dot)
    {
        if (id_var_entry[Class] != Loc || id_var_entry[Type] != STRUCT_TYPE)
        {
            printf("line %ld: error: '%s' is not a struct.\n", line, (char *)id_var_entry[Name]);
            exit(-1);
        }
        assert(Dot);
        assert(Id);
        int member_offset = find_struct_member(id_var_entry, (char *)symbol_ptr[Name]);
        if (member_offset < 0)
        {
            printf("line %ld: error: struct member '%s' not found.\n", line, (char *)symbol_ptr[Name]);
            exit(-1);
        }
        assert(Assign);
        *++code = LEA;
        *++code = id_var_entry[Value]; // Load base address of struct
        *++code = LEA_STRUCT;          // Calculate member address
        *++code = member_offset;       // Member offset as operand
        *++code = PUSH;                // Push member address onto stack
        parse_expr();                  // RHS expression, result in ax
        *++code = SI;                  // Store ax into address popped from stack
    }
    // Handle simple variable assignment: var = expr
    else if (token == Assign)
    {
        assert(Assign); // Assign is an enum value
        if (id_var_entry[Class] != Loc)
        {
            printf("line %ld: error: cannot assign to non-variable or undeclared '%s'.\n", line,
                   (char *)id_var_entry[Name]);
            exit(-1);
        }
        *++code = LEA;
        *++code = id_var_entry[Value];
        *++code = PUSH;
        parse_expr();
        *++code = SI;
    }
    else
    {
        printf("line %ld: error: expected assignment operator after identifier.\n", line);
        exit(-1);
    }
}

/*
 * 解析函数调用语句体
 * 功能：处理函数调用的参数解析和代码生成
 *
 * 参数：func_id_entry - 函数标识符的符号表条目
 *
 * 函数调用语法：function_name(arg1, arg2, ...)
 *
 * 工作原理：
 * 1. 解析参数列表，将每个参数值压栈
 * 2. 生成函数调用指令
 * 3. 如果有参数，生成参数清理指令
 */
void parse_func_call_stmt_body(int *func_id_entry)
{
    assert('(');
    int arg_count = 0; // 参数计数器

    // 解析参数列表
    if (token != ')')
    {
        while (1)
        {
            parse_expr();       // 解析参数表达式
            *++code = PUSH;     // 将参数值压栈
            arg_count++;
            if (token == ',')
                assert(',');    // 处理参数分隔符
            else
                break;          // 参数列表结束
        }
    }
    assert(')');

    // 检查标识符是否为函数
    if (func_id_entry[Class] != Fun)
    {
        printf("line %ld: error: '%s' is not a function.\n", line, (char *)func_id_entry[Name]);
        exit(-1);
    }

    // 生成函数调用指令
    *++code = CALL;                    // 调用指令
    *++code = func_id_entry[Value];    // 函数地址
    if (arg_count > 0)
    {
        *++code = DARG;                // 参数清理指令
        *++code = arg_count;           // 参数数量
    }
}

/*
 * 解析if语句
 * 功能：处理条件分支语句的解析和代码生成
 *
 * if语句语法：
 * if (condition) { statements } [else { statements }]
 *
 * 工作原理：
 * 1. 解析条件表达式
 * 2. 生成条件跳转指令（条件为假时跳转）
 * 3. 解析if分支的语句
 * 4. 如果有else分支，生成跳过else的跳转指令
 * 5. 解析else分支的语句
 * 6. 回填跳转地址（地址回填技术）
 */
void parse_if_stmt()
{
    assert(If);
    assert('(');
    parse_expr();                      // 解析条件表达式
    assert(')');

    *++code = JZ;                      // 生成零跳转指令（条件为假时跳转）
    int *patch_jz = ++code;            // 保存跳转地址的位置，稍后回填

    assert('{');
    parse_stmt_list();                 // 解析if分支的语句列表
    assert('}');

    if (token == Else)                 // 处理else分支
    {
        assert(Else);
        *++code = JMP;                 // 生成无条件跳转指令（跳过else分支）
        int *patch_jmp_endif = ++code; // 保存跳转地址的位置
        *patch_jz = (int64_t)(code + 1); // 回填JZ的跳转地址（跳到else分支）

        assert('{');
        parse_stmt_list();             // 解析else分支的语句列表
        assert('}');
        *patch_jmp_endif = (int64_t)(code + 1); // 回填JMP的跳转地址（跳到if语句结束）
    }
    else
    {
        *patch_jz = (int64_t)(code + 1); // 回填JZ的跳转地址（跳到if语句结束）
    }
}

/*
 * 解析while循环语句
 * 功能：处理循环语句的解析和代码生成
 *
 * while语句语法：
 * while (condition) { statements }
 *
 * 工作原理：
 * 1. 记录循环开始位置
 * 2. 解析循环条件表达式
 * 3. 生成条件跳转指令（条件为假时跳出循环）
 * 4. 解析循环体语句
 * 5. 生成跳回循环开始的指令
 * 6. 回填跳出循环的地址
 */
void parse_while_stmt()
{
    assert(While);
    int *loop_start = code + 1;        // 记录循环开始位置

    assert('(');
    parse_expr();                      // 解析循环条件表达式
    assert(')');

    *++code = JZ;                      // 生成零跳转指令（条件为假时跳出循环）
    int *patch_jz_end = ++code;        // 保存跳转地址的位置，稍后回填

    assert('{');
    parse_stmt_list();                 // 解析循环体语句列表
    assert('}');

    *++code = JMP;                     // 生成无条件跳转指令（跳回循环开始）
    *++code = (int64_t)loop_start;     // 跳转到循环开始位置
    *patch_jz_end = (int64_t)(code + 1); // 回填跳出循环的地址
}

/*
 * 解析输入语句
 * 功能：处理input语句，从标准输入读取数据到变量
 *
 * input语句语法：
 * input(var1, var2, ...);
 *
 * 工作原理：
 * 1. 解析变量列表
 * 2. 为每个变量生成输入指令序列
 * 3. 将读取的值存储到对应变量中
 */
void parse_input_stmt()
{
    assert(Input);
    assert('(');
    int first = 1; // 第一个变量标志

    while (1)
    {
        if (!first)
            assert(',');           // 处理变量分隔符
        first = 0;
        assert(Id);

        // 检查标识符是否为局部变量
        if (symbol_ptr[Class] != Loc)
        {
            printf("line %ld: error: 'input' target '%s' must be a local variable.\n", line, (char *)symbol_ptr[Name]);
            exit(-1);
        }

        // 生成输入指令序列
        *++code = LEA;                     // 加载变量地址到ax
        *++code = symbol_ptr[Value];       // 变量的栈偏移量
        *++code = PUSH;                    // 将地址压栈
        *++code = READ_INT_AX;             // 从标准输入读取整数到ax
        *++code = SI;                      // 将ax中的值存储到栈顶地址指向的变量

        if (token != ',')
            break;                         // 变量列表结束
    }
    assert(')');
}

/*
 * 解析输出语句
 * 功能：处理output语句，将表达式结果输出到标准输出
 *
 * output语句语法：
 * output(expr1, expr2, ...);
 *
 * 工作原理：
 * 1. 解析表达式列表
 * 2. 为每个表达式生成计算和输出指令
 * 3. 将计算结果输出到标准输出
 */
void parse_output_stmt()
{
    assert(Output);
    assert('(');
    int first = 1; // 第一个表达式标志

    while (1)
    {
        if (!first)
            assert(',');               // 处理表达式分隔符
        first = 0;
        parse_expr();                  // 解析表达式，结果存入ax
        *++code = PRINT_INT_AX;        // 生成输出指令，打印ax中的值
        if (token != ',')
            break;                     // 表达式列表结束
    }
    assert(')');
}

/*
 * 解析语句
 * 功能：语句解析的分发器，根据当前标记类型调用相应的解析函数
 *
 * 支持的语句类型：
 * - let语句：变量声明
 * - 标识符语句：赋值或函数调用
 * - if语句：条件分支
 * - while语句：循环
 * - input语句：输入
 * - output语句：输出
 * - return语句：函数返回（仅在函数体内）
 */
void parse_stmt()
{
    if (token == Let)
        parse_declare_stmt();              // 解析变量声明语句
    else if (token == Id)
    {
        int *id_entry = symbol_ptr;        // 保存标识符的符号表条目
        tokenize();
        if (token == Assign || token == '[' || token == Dot)
            parse_assign_stmt_body(id_entry); // 处理赋值、数组访问或结构体访问
        else if (token == '(')
            parse_func_call_stmt_body(id_entry); // 处理函数调用
        else
        {
            printf("line %ld: error: unexpected token '%ld' after identifier in statement.\n",
                   line, token);
            exit(-1);
        }
    }
    else if (token == If)
        parse_if_stmt();                   // 解析if语句
    else if (token == While)
        parse_while_stmt();                // 解析while语句
    else if (token == Input)
        parse_input_stmt();                // 解析input语句
    else if (token == Output)
        parse_output_stmt();               // 解析output语句
    else if (token == Return)
    {
        // return语句只能在函数体内使用
        if (!current_parsing_func_body)
        {
            printf("line %ld: error: 'return' statement not allowed outside function body.\n",
                   line);
            exit(-1);
        }
        // 这种情况理论上不应该到达，因为parse_stmt_list会在遇到Return前停止
        printf("line %ld: error: unexpected 'return' here, should be at end of func body.\n",
               line);
        exit(-1);
    }
    else
    {
        printf("line %ld: error: unknown statement starting with token %ld.\n", line, token);
        exit(-1);
    }
}

/*
 * 解析语句列表
 * 功能：解析一系列语句，直到遇到结束标记
 *
 * 工作原理：
 * 1. 循环解析语句，直到遇到 '}' 或 'return' 或文件结束
 * 2. 对于简单语句要求分号，复合语句（if、while）不需要
 * 3. 确保语句列表不为空（除非函数体只有return语句）
 *
 * 语法规则：
 * - 简单语句后必须有分号
 * - 复合语句（if、while）后不需要分号
 * - 语句列表不能为空
 */
void parse_stmt_list()
{
    int stmt_count = 0; // 语句计数器

    // 循环解析语句，直到遇到结束条件
    while (token != '}' && !(current_parsing_func_body && token == Return) && token != 0)
    {
        int stmt_token = token; // 记住当前语句的类型
        parse_stmt();           // 解析单个语句

        // // 只有简单语句需要分号，复合语句不需要
        // if (stmt_token != If && stmt_token != While)
        // {
        //     assert(';');        // 要求分号
        // }
        assert(';');
        stmt_count++;
    }

    // 检查语句列表是否为空
    if (stmt_count == 0 && !(current_parsing_func_body && token == Return))
    {
        printf("line %ld: error: statement list cannot be empty.\n", line);
        exit(-1);
    }
}

/*
 * 解析参数列表
 * 功能：解析函数定义中的参数列表
 *
 * 参数列表语法：(param1, param2, ...)
 *
 * 返回值：参数数量
 *
 * 工作原理：
 * 1. 解析参数名称列表
 * 2. 检查参数数量限制和重复定义
 * 3. 为每个参数分配栈偏移量
 * 4. 参数在栈中的布局：第一个参数在最高地址
 *
 * 栈布局示例（func(a,b,c)调用后）：
 * bp+4: 参数a的值
 * bp+3: 参数b的值
 * bp+2: 参数c的值
 */
int parse_param_list() // 返回参数数量
{
    int param_count = 0; // 参数计数器
    int first = 1;       // 第一个参数标志

    // 注意：param_sym_entries数组由调用者（parse_func_def）重置

    while (token != ')')
    {
        if (!first)
            assert(',');         // 处理参数分隔符
        first = 0;
        assert(Id);

        // 检查参数数量限制
        if (param_count >= MAX_PARAMS)
        {
            printf("line %ld: error: too many parameters for function (max %d).\n", line, MAX_PARAMS);
            exit(-1);
        }

        // 检查参数名称是否重复
        // 这里检查的是同一个符号表条目是否被重复添加为参数
        for (int k = 0; k < param_count; ++k)
        {
            if (param_sym_entries[k] == symbol_ptr)
            {
                printf("line %ld: error: duplicate parameter name '%s' in function definition.\n", line,
                       (char *)symbol_ptr[Name]);
                exit(-1);
            }
        }

        param_sym_entries[param_count] = symbol_ptr; // 存储当前参数的符号表条目

        // 在主符号表中标记该标识符为局部变量
        symbol_ptr[Class] = Loc;
        symbol_ptr[Type] = INT_TYPE;
        // symbol_ptr[Value]（栈偏移量）将在计算完所有参数后设置

        param_count++;

        if (token != ',')
            break;               // 参数列表结束
    }

    // 分配正确的栈偏移量
    // 参数压栈顺序：第一个参数在最高地址，最后一个参数在最低地址
    // 例如func(a,b,c)调用后的栈布局（相对于新的bp）：
    // bp+4: 参数a的值
    // bp+3: 参数b的值
    // bp+2: 参数c的值
    // param_sym_entries[i]是定义顺序中第(i+1)个参数的符号表条目
    for (int i = 0; i < param_count; ++i)
    {
        param_sym_entries[i][Value] = (param_count - i) + 1;
    }
    return param_count;
}

/*
 * 解析函数定义
 * 功能：处理函数定义的完整解析过程
 *
 * 函数定义语法：
 * func function_name(param1, param2, ...) {
 *     statements
 *     return expression;
 * }
 *
 * 工作原理：
 * 1. 解析函数名和参数列表
 * 2. 生成函数入口代码（NVAR指令）
 * 3. 解析函数体语句
 * 4. 处理return语句
 * 5. 回填局部变量数量
 */
void parse_func_def()
{
    // 重置当前函数的临时参数存储
    for (int i = 0; i < MAX_PARAMS; ++i)
        param_sym_entries[i] = NULL;

    assert(FuncKw);
    assert(Id);

    // 检查函数是否重复定义
    if (symbol_ptr[Class] == Fun)
    {
        printf("line %ld: error: duplicate function definition '%s'.\n", line, (char *)symbol_ptr[Name]);
        exit(-1);
    }

    // 设置函数符号表条目
    symbol_ptr[Class] = Fun;
    symbol_ptr[Type] = INT_TYPE;
    symbol_ptr[Value] = (int64_t)(code + 1); // 函数入口地址

    current_func_var_count = 0; // 重置局部变量计数器

    // 解析参数列表
    assert('(');
    if (token != ')')
        parse_param_list();
    assert(')');

    // 解析函数体
    assert('{');
    current_parsing_func_body = 1; // 标记正在解析函数体

    // 生成函数入口指令
    *++code = NVAR;                    // 新建变量指令
    int *nvar_patch_addr = ++code;     // 保存需要回填的地址
    *nvar_patch_addr = 0;              // 初始化为0，稍后回填

    parse_stmt_list();                 // 解析函数体语句列表

    *nvar_patch_addr = current_func_var_count; // 回填局部变量数量

    // 处理return语句
    assert(Return);
    parse_expr();                      // 解析返回表达式
    assert(';');
    *++code = RET;                     // 生成返回指令

    assert('}');
    current_parsing_func_body = 0;     // 标记函数体解析结束
}

/*
 * 解析结构体定义（新增功能）
 * 功能：处理struct语句，定义新的结构体类型
 *
 * 结构体语法：
 * struct StructName {
 *     member1;
 *     member2;
 *     ...
 * }
 *
 * 工作原理：
 * 1. 检查结构体名称是否重复
 * 2. 为结构体分配索引
 * 3. 解析所有成员定义
 * 4. 将结构体信息存储到符号表和成员数组中
 */
void parse_struct_def()
{
    assert(StructKw);
    assert(Id);

    if (symbol_ptr[Class] == Struct)
    {
        printf("line %ld: error: duplicate struct definition '%s'.\n", line, (char *)symbol_ptr[Name]);
        exit(-1);
    }

    if (struct_count >= MAX_STRUCTS)
    {
        printf("line %ld: error: too many struct definitions (max %d).\n", line, MAX_STRUCTS);
        exit(-1);
    }

    int *struct_sym = symbol_ptr;
    struct_sym[Class] = Struct;
    struct_sym[Type] = STRUCT_TYPE;
    current_struct_def = struct_sym;

    int current_struct_index = struct_count++;

    // Reset struct members array for this struct
    for (int i = 0; i < MAX_STRUCT_MEMBERS; ++i)
        struct_members[current_struct_index][i] = NULL;

    assert('{');

    int member_count = 0;
    while (token != '}' && token != 0)
    {
        assert(Id);

        if (member_count >= MAX_STRUCT_MEMBERS)
        {
            printf("line %ld: error: too many members in struct (max %d).\n", line, MAX_STRUCT_MEMBERS);
            exit(-1);
        }

        // Check for duplicate member names
        for (int k = 0; k < member_count; ++k)
        {
            if (struct_members[current_struct_index][k] == symbol_ptr)
            {
                printf("line %ld: error: duplicate member name '%s' in struct definition.\n", line,
                       (char *)symbol_ptr[Name]);
                exit(-1);
            }
        }

        struct_members[current_struct_index][member_count] = symbol_ptr;
        symbol_ptr[Class] = Loc;          // Mark as local for now
        symbol_ptr[Type] = INT_TYPE;      // Only int members for now
        symbol_ptr[Value] = member_count; // Store member offset

        member_count++;
        assert(';');
    }

    assert('}');

    if (member_count == 0)
    {
        printf("line %ld: error: struct cannot be empty.\n", line);
        exit(-1);
    }

    struct_sym[Extra] = member_count;         // Store member count
    struct_sym[Value] = current_struct_index; // Store struct index instead of pointer
}

/*
 * 查找结构体成员（新增功能）
 * 功能：在指定结构体中查找成员，返回成员的偏移量
 *
 * 参数：
 * - struct_var_sym: 结构体变量的符号表条目
 * - member_name: 要查找的成员名称
 *
 * 返回值：
 * - 成功：成员在结构体中的偏移量（从0开始）
 * - 失败：-1（结构体类型错误或成员不存在）
 *
 * 工作原理：
 * 1. 验证变量确实是结构体类型
 * 2. 获取结构体定义信息
 * 3. 在成员数组中查找指定名称的成员
 * 4. 返回成员的偏移量
 */
int find_struct_member(int *struct_var_sym, char *member_name)
{
    // 检查是否为结构体类型
    if (struct_var_sym[Type] != STRUCT_TYPE)
        return -1;

    // 获取结构体定义的符号表条目
    int *struct_def_sym = (int *)struct_var_sym[Extra];
    if (!struct_def_sym)
        return -1;

    int struct_index = struct_def_sym[Value]; // 获取结构体索引
    int member_count = struct_def_sym[Extra]; // 获取成员数量

    // 在结构体成员数组中查找指定名称的成员
    for (int i = 0; i < member_count; ++i)
    {
        if (struct_members[struct_index][i] &&
            strcmp((char *)struct_members[struct_index][i][Name], member_name) == 0)
        {
            return i; // 返回成员偏移量
        }
    }

    return -1; // 成员未找到
}

/*
 * 主语法分析函数
 * 功能：解析整个L25程序的语法结构
 *
 * L25程序结构：
 * program ProgramName {
 *     [struct definitions]
 *     [function definitions]
 *     main {
 *         statements
 *     }
 * }
 *
 * 工作流程：
 * 1. 初始化解析状态
 * 2. 解析程序头部
 * 3. 解析结构体和函数定义
 * 4. 解析main块
 * 5. 生成程序退出代码
 */
void parse()
{
    line = 1;    // 初始化行号
    token = 1;   // 初始化标记
    tokenize();  // 读取第一个标记

    // 解析程序头部：program ProgramName {
    assert(Program);
    assert(Id);
    assert('{');

    // 解析结构体定义和函数定义
    while (token == StructKw || token == FuncKw)
    {
        if (token == StructKw)
            parse_struct_def();    // 解析结构体定义
        else
            parse_func_def();      // 解析函数定义
    }

    // 解析main块
    assert(MainKw);
    entry_main_addr = code + 1;    // 设置main函数入口地址
    current_func_var_count = 0;    // 重置局部变量计数器

    assert('{');
    current_parsing_func_body = 0; // main块不是函数体

    // 生成main块的入口代码
    *++code = NVAR;                        // 新建变量指令
    int *main_nvar_patch_addr = ++code;    // 保存需要回填的地址
    *main_nvar_patch_addr = 0;             // 初始化为0，稍后回填

    parse_stmt_list();                     // 解析main块的语句列表

    *main_nvar_patch_addr = current_func_var_count; // 回填局部变量数量

    assert('}'); // main块结束
    assert('}'); // 程序结束

    // 生成程序退出代码
    *++code = IMM;   // 加载退出码0
    *++code = 0;
    *++code = PUSH;  // 将退出码压栈
    *++code = EXIT;  // 程序退出指令

    // 检查是否有多余的标记
    if (token != 0)
    {
        printf("line %ld: error: unexpected tokens after end of program.\n", line);
        exit(-1);
    }
}

/* ========== 关键字初始化 ========== */

/*
 * 关键字设置函数
 * 功能：将L25语言的所有关键字预先加载到符号表中
 *
 * 工作原理：
 * 1. 定义所有关键字字符串和对应的标记类型
 * 2. 逐个将关键字"词法分析"并添加到符号表
 * 3. 这样在后续的词法分析过程中，遇到关键字时就能正确识别
 *
 * 这个函数必须在开始解析源代码之前调用
 */
void keyword()
{
    int i; // 循环计数器

    // L25语言的所有关键字字符串数组
    char *keywords_str[] = {"program", "func",  "main",  "let",    "if",     "else",
                            "return",  "while", "input", "output", "struct", NULL};

    // 对应的标记类型数组（必须与上面的字符串数组一一对应）
    int keyword_tokens[] = {Program, FuncKw, MainKw, Let, If, Else, Return, While, Input, Output, StructKw};

    char *saved_src = src; // 保存当前源代码指针

    // 逐个处理每个关键字
    for (i = 0; keywords_str[i]; ++i)
    {
        src = keywords_str[i];              // 临时设置源代码指针指向关键字
        tokenize();                         // 对关键字进行词法分析，添加到符号表
        symbol_ptr[Token] = keyword_tokens[i]; // 设置正确的标记类型
    }

    src = saved_src; // 恢复原来的源代码指针
}

/* ========== 虚拟机初始化 ========== */

/*
 * 虚拟机内存初始化函数
 * 功能：为虚拟机的各个内存段分配空间并初始化
 *
 * 返回值：
 * - 0: 初始化成功
 * - -1: 内存分配失败
 *
 * 内存段说明：
 * - code段：存储编译后的虚拟机指令
 * - data段：存储程序数据（L25中很少使用）
 * - stack段：程序运行时的栈空间
 * - symbol_table：符号表，存储标识符信息
 */
#ifdef int
#undef int
#endif
int init_vm()
{
#define int int64_t // 在函数内部重新定义int为int64_t

    // 分配代码段内存
    // code用于存储生成的虚拟机指令，code_dump用于调试输出
    if (!(code = code_dump = malloc(MAX_SIZE)))
    {
        printf("malloc failed for code segment\n");
        return -1;
    }


    // 分配栈段内存
    if (!(stack = malloc(MAX_SIZE)))
    {
        printf("malloc failed for stack segment\n");
        return -1;
    }

    // 分配符号表内存（大小为其他段的1/8）
    if (!(symbol_table = malloc(MAX_SIZE / 8)))
    {
        printf("malloc failed for symbol_table\n");
        return -1;
    }

    // 将所有内存段初始化为0
    memset(code, 0, MAX_SIZE);
    memset(stack, 0, MAX_SIZE);
    memset(symbol_table, 0, MAX_SIZE / 8);

    return 0; // 初始化成功
#undef int    // 清理局部定义
}
#define int int64_t // 恢复全局定义

/* ========== 虚拟机执行引擎 ========== */

/*
 * L25虚拟机主执行函数
 * 功能：执行由语法分析器生成的虚拟机指令
 *
 * 参数：
 * - argc_param: 命令行参数数量（未使用）
 * - argv_param: 命令行参数数组（未使用）
 *
 * 返回值：程序退出码
 *
 * 工作原理：
 * 1. 初始化虚拟机寄存器和栈
 * 2. 从main函数入口开始执行指令
 * 3. 使用取指-译码-执行循环处理每条指令
 * 4. 直到遇到EXIT指令或错误为止
 */
int run_vm(int argc_param, char **argv_param)
{
    int op; // 当前指令操作码

    // 初始化栈指针：栈从高地址向低地址增长
    bp = sp = (int *)((int64_t)stack + MAX_SIZE);

    // 检查main函数入口点是否已设置
    if (!entry_main_addr)
    {
        printf("L25 main block entry point not defined (parser error or no main block).\n");
        exit(-1);
    }
    pc = entry_main_addr; // 设置程序计数器指向main函数

    cycle = 0; // 指令执行计数器

    // 主执行循环：取指-译码-执行
    while (1)
    {
        cycle++;
        op = *pc++; // 取指：获取当前指令并移动程序计数器

        // 译码和执行：根据指令类型执行相应操作
        if (op == IMM)
            ax = *pc++; // 立即数加载：将下一个值加载到ax寄存器
        else if (op == LEA)
            ax = (int64_t)(bp + *pc++); // 地址计算：计算相对于bp的地址
        else if (op == JMP)
            pc = (int *)*pc; // 无条件跳转：直接跳转到指定地址
        else if (op == JZ)
            pc = ax ? pc + 1 : (int *)*pc; // 零跳转：ax为0时跳转
        else if (op == JNZ)
            pc = ax ? (int *)*pc : pc + 1; // 非零跳转：ax非0时跳转
        else if (op == CALL)
        {
            *--sp = (int64_t)(pc + 1);
            pc = (int *)*pc;
        } // Cast to int64_t
        else if (op == NVAR)
        {
            *--sp = (int64_t)bp;
            bp = sp;
            sp = sp - *pc++;
        } // Cast to int64_t
        else if (op == DARG)
            sp = sp + *pc++;
        else if (op == RET)
        {
            sp = bp;
            bp = (int *)*sp++;
            pc = (int *)*sp++;
        }
        else if (op == LI)
            ax = *(int *)ax; // ax is int64_t, *(int*)ax dereferences int64_t*
        else if (op == SI)
            *(int *)*sp++ = ax;
        else if (op == PUSH)
            *--sp = ax;
        else if (op == ADD)
            ax = *sp++ + ax;
        else if (op == SUB)
            ax = *sp++ - ax;
        else if (op == MUL)
            ax = *sp++ * ax;
        else if (op == DIV)
        {
            if (ax == 0)
            {
                fprintf(stderr, "Runtime Error: Division by zero (cycle: %ld).\n", cycle);
                exit(-2);
            }
            ax = *sp++ / ax;
        }
        else if (op == EQ)
            ax = (*sp++ == ax);
        else if (op == NE)
            ax = (*sp++ != ax);
        else if (op == LT)
            ax = (*sp++ < ax);
        else if (op == GT)
            ax = (*sp++ > ax);
        else if (op == LE)
            ax = (*sp++ <= ax);
        else if (op == GE)
            ax = (*sp++ >= ax);
        else if (op == EXIT)
        {
            printf("L25 program exited with code: %ld (cycles: %ld)\n", *sp, cycle);
            return *sp;
        }
        else if (op == READ_INT_AX)
        {
            fflush(stdout);
            if (scanf("%ld", &ax) != 1)
            { // ax is int64_t
                fprintf(stderr,
                        "Runtime Error: Failed to read an integer from input (cycle: "
                        "%ld).\n",
                        cycle);
                ax = 0;
            }
        }
        else if (op == PRINT_INT_AX)
        {
            printf("%ld\n", ax); // ax is int64_t
            fflush(stdout);
        }
        else if (op == LEA_ARRAY)
        {
            // ax has base address, stack top has index
            int index = *sp++;
            ax = ax + index * sizeof(int64_t); // Calculate element address
        }
        else if (op == LEA_STRUCT)
        {
            // ax has base address, next instruction has member offset
            int offset = *pc++;
            ax = ax + offset * sizeof(int64_t); // Calculate member address
        }
        else
        {
            printf("Runtime Error: Unknown instruction %ld at pc=%p (cycle: %ld)\n", op, (void *)(pc - 1), cycle);
            return -1;
        }
    }
    return 0;
}

/* ========== 调试：输出汇编指令 ========== */

/*
 * 汇编代码输出函数（调试用）
 * 功能：将生成的虚拟机指令以汇编格式输出到文件
 *
 * 输出文件：assemble.l25
 *
 * 工作原理：
 * 1. 遍历代码段中的所有指令
 * 2. 将指令操作码转换为助记符
 * 3. 对于有操作数的指令，同时输出操作数
 * 4. 按行格式化输出到文件
 *
 * 这个函数主要用于调试和理解生成的代码
 */
void write_as()
{
    int fd; // 文件描述符
    char buffer[100]; // 输出缓冲区

    // 指令助记符字符串，每个助记符占5个字符（包括逗号）
    char *insts = "IMM ,LEA ,JMP ,JZ  ,JNZ ,CALL,NVAR,DARG,RET ,LI  ,SI  ,PUSH,"
                  "ADD ,SUB ,MUL ,DIV ,EQ  ,NE  ,LT  ,GT  ,LE  ,GE  ,EXIT,"
                  "RDAX,PTAX,LEAR,LEAS,";

    // 创建输出文件
    if ((fd = open("assemble.l25", O_WRONLY | O_CREAT | O_TRUNC, 0644)) < 0)
    {
        printf("Failed to open assemble.l25 for writing.\n");
        return;
    }

    int *current_code_ptr = code_dump; // 当前代码指针
    int instruction_idx = 0;           // 指令序号

    // 遍历所有生成的指令
    while (current_code_ptr < code)
    {
        instruction_idx++;
        int current_op = *current_code_ptr; // 当前指令操作码

        // 将操作码转换为助记符
        char mnemonic_buffer[6];
        if (current_op >= 0 && (current_op * 5 + 4) < strlen(insts))
        {
            strncpy(mnemonic_buffer, insts + (current_op * 5), 4);
            mnemonic_buffer[4] = '\0';
        }
        else
        {
            strcpy(mnemonic_buffer, "UNKN"); // 未知指令
        }

        // 输出指令序号和助记符
        sprintf(buffer, "%04ld %-4s", instruction_idx, mnemonic_buffer);
        write(fd, buffer, strlen(buffer));

        current_code_ptr++;

        // 检查是否需要输出操作数
        if (current_op == IMM || current_op == LEA || current_op == JZ || current_op == JNZ || current_op == CALL ||
            current_op == NVAR || current_op == DARG || current_op == JMP || current_op == LEA_STRUCT)
        {
            if (current_code_ptr < code)
            {
                sprintf(buffer, " %ld\n", *current_code_ptr); // 输出操作数
                current_code_ptr++;
            }
            else
            {
                strcpy(buffer, " (missing operand)\n"); // 操作数缺失
            }
        }
        else
        {
            strcpy(buffer, "\n"); // 无操作数指令
        }
        write(fd, buffer, strlen(buffer));
    }
    close(fd);
    printf("Assembly-like output written to assemble.l25\n");
}

/* ========== 源代码加载 ========== */

/*
 * 源代码文件加载函数
 * 功能：从文件系统读取L25源代码文件到内存中
 *
 * 参数：file - 源代码文件路径
 *
 * 返回值：
 * - 0: 加载成功
 * - -1: 加载失败（文件不存在、内存不足、读取失败等）
 *
 * 工作流程：
 * 1. 打开源代码文件
 * 2. 分配内存缓冲区
 * 3. 读取文件内容到缓冲区
 * 4. 添加字符串结束符
 * 5. 清理资源
 */
#ifdef int
#undef int
#endif
int load_src(char *file)
{
#define int int64_t // 在函数内部重新定义int为int64_t
    int fd;         // 文件描述符
    int cnt;        // 读取的字节数

    // 打开源代码文件
    if ((fd = open(file, O_RDONLY)) < 0)
    {
        printf("Error: Could not open source file '%s'\n", file);
        return -1;
    }

    // 分配源代码缓冲区
    // src用于词法分析时移动，src_dump保留原始地址用于释放内存
    if (!(src = src_dump = malloc(MAX_SIZE)))
    {
        printf("Error: Could not malloc for source code buffer\n");
        close(fd);
        return -1;
    }

    // 读取文件内容到缓冲区
    if ((cnt = read(fd, src, MAX_SIZE - 1)) <= 0)
    {
        printf("Error: Could not read source code from '%s' or file is empty\n", file);
        free(src_dump);
        src = src_dump = NULL;
        close(fd);
        return -1;
    }

    // 添加字符串结束符
    // 这很重要，将内存数据转换为标准C字符串
    // 使得后续的字符串处理函数能正确工作
    src[cnt] = 0;
    close(fd);

    return 0; // 加载成功
#undef int    // 清理局部定义
}
#define int int64_t // 恢复全局定义

/* ========== 主程序入口 ========== */

#ifdef int
#undef int // 在main函数签名前取消int的重定义
#endif

/*
 * L25编译器和虚拟机的主入口函数
 * 功能：协调整个编译和执行过程
 *
 * 参数：
 * - argc: 命令行参数数量
 * - argv: 命令行参数数组
 *
 * 返回值：程序退出码
 *
 * 执行流程：
 * 1. 检查命令行参数
 * 2. 初始化内存和虚拟机
 * 3. 加载源代码文件
 * 4. 设置关键字
 * 5. 进行语法分析，生成虚拟机指令
 * 6. 执行生成的指令
 * 7. 返回执行结果
 */
int main(int argc, char *argv[])
{
#define int int64_t // 在main函数内部重新定义int为int64_t

    // 检查命令行参数
    if (argc < 2)
    {
        printf("Usage: %s <sourcefile.l25>\n", argv[0]);
        return -1;
    }

    // 设置内存大小：2MB用于代码段、数据段、栈等
    MAX_SIZE = 256 * 1024 * 8;

    // 第一阶段：加载源代码
    if (load_src(argv[1]) != 0)
        return -1;

    // 第二阶段：初始化虚拟机内存
    if (init_vm() != 0)
        return -1;

    // 第三阶段：设置语言关键字
    keyword(); // 将L25语言的关键字预先加载到符号表中

    // 第四阶段：语法分析和代码生成
    parse();   // 解析源代码，生成虚拟机指令

    // 第五阶段：输出汇编代码（用于调试）
    write_as();

    // 第六阶段：执行生成的虚拟机指令
    return (int)run_vm(argc - 1, argv + 1);
}

// No need to #undef int at the very end of the file if main is the last
// function that needs the standard signature and there are no further standard
// library interactions that would be confused by 'int' being 'int64_t'.