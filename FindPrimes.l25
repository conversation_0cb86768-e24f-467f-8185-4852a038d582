program PrimeNumberFinder {

  // Function to check if a number is prime
  // Returns 1 if prime, 0 otherwise
  func isPrime(num) {
    let primeFlag; // Will be 1 if prime, 0 if not
    let i;         // Divisor to check

    if (num <= 1) {
      primeFlag = 0; // Numbers less than or equal to 1 are not prime
    } else {
      if (num == 2) {
        primeFlag = 1; // 2 is the smallest prime number
      } else {
        // For any other number, assume it's prime initially
        primeFlag = 1; 
        i = 2;
        // Check for divisibility from 2 up to num-1.
        // If a divisor is found, the number is not prime.
        while (i < num) { 
          // Check if num is divisible by i using integer division:
          // If (num / i) * i == num, it means i divides num exactly.
          if ((num / i) * i == num) {
            primeFlag = 0; // Found a divisor, so not prime
            i = num;       // Set i to num to break out of the while loop
                           // as L25 doesn't have a 'break' statement.
          } else {
            primeFlag = primeFlag;
            // No action needed, continue to the next potential divisor
          }; // End of inner if-else for divisibility check
          
          // Only increment i if we haven't found a divisor yet (primeFlag is still 1)
          // If primeFlag became 0, i was set to num, and we want the loop to terminate.
          if (primeFlag == 1) { 
             i = i + 1;
          }; 
          
        }; // End of while loop checking divisors
      }; // End of else (handles num != 2)
    }; // End of else (handles num > 1)
    return primeFlag;
  }

  // Function to print a program header (using numbers for messages)
  func printHeader() {
    output(99999); // Special number indicating start of header
    output(101);   // "Message Code: Prime Number Finder Program"
    output(102);   // "Message Code: ---------------------------------"
    return 0;     // Functions must return a value
  }

  main {
    let upperLimit;
    let currentNumber;
    let isCurrentNumberPrime;
    let primesFoundCount;

    printHeader(); // Call the function to print the header

    output(201);   // "Message Code: Enter the upper limit for finding primes:"
    input(upperLimit);

    if (upperLimit < 2) {
      output(301); // "Message Code: No primes to find in this range (limit < 2)."
    } else {
      output(202);   // "Message Code: Prime numbers up to"
      output(upperLimit);
      output(203);   // "Message Code: are:"
      
      currentNumber = 2;      // Start checking from the first prime number
      primesFoundCount = 0;

      while (currentNumber <= upperLimit) {
        isCurrentNumberPrime = isPrime(currentNumber); // Call the isPrime function
        if (isCurrentNumberPrime == 1) {
          output(currentNumber); // Print the prime number
          primesFoundCount = primesFoundCount + 1;
        }; // End of if (isCurrentNumberPrime == 1)
        currentNumber = currentNumber + 1;
      }; // End of while (currentNumber <= upperLimit)

      output(204);   // "Message Code: Total prime numbers found:"
      output(primesFoundCount);
    }; // End of else (upperLimit >= 2)

    output(88888); // Special number indicating end of program
  }
}