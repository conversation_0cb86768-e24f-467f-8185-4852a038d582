#include <fcntl.h>    // 文件操作相关函数
#include <memory.h>   // 内存操作函数
#include <stdint.h>   // 标准整数类型定义
#include <stdio.h>    // 标准输入输出函数
#include <stdlib.h>   // 标准库函数（malloc、exit等）
#include <string.h>   // 字符串操作函数
#include <unistd.h>   // Unix标准函数

char *src,      // 源代码指针：指向当前正在分析的字符
     *src_dump; // 源代码备份：保存原始源代码起始地址
int MAX_SIZE; // 内存段的最大大小

#ifdef int
#undef int
#endif

void tokenize()
{
    char *ch_ptr;         // 指向当前标识符开始位置的指针
    int current_hash_val; // 当前标识符的哈希值累加器

    // 主循环：逐个处理源代码字符
    while ((token = *src++))
    {
        // 处理换行符：增加行号计数，用于错误报告
        if (token == '\n')
        {
            line++;
        }
        // 跳过空白字符：空格、制表符、回车符
        else if (token == ' ' || token == '\t' || token == '\r')
        {
            // 什么都不做，继续下一个字符
        }
        // 处理标识符：以字母或下划线开头，后面可以跟字母、数字、下划线
        else if ((token >= 'a' && token <= 'z') || (token >= 'A' && token <= 'Z') || (token == '_'))
        {
            ch_ptr = src - 1;         // 记录标识符的起始位置
            current_hash_val = token; // 用第一个字符初始化哈希值

            // 继续读取标识符的剩余部分
            while ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z') || (*src >= '0' && *src <= '9') ||
                   (*src == '_'))
            {
                current_hash_val = current_hash_val * 147 + *src++; // 计算哈希值
            }
            current_hash_val = (current_hash_val << 6) + (src - ch_ptr); // 最终哈希值

            // 在符号表中查找该标识符
            symbol_ptr = symbol_table;
            while (symbol_ptr[Token])
            {
                // 比较哈希值和字符串内容
                if (current_hash_val == symbol_ptr[Hash] && !memcmp((char *)symbol_ptr[Name], ch_ptr, src - ch_ptr))
                {
                    token = symbol_ptr[Token]; // 找到了，使用已有的标记类型
                    return;
                }
                symbol_ptr = symbol_ptr + SymSize; // 移动到下一个符号表条目
            }
            // 标识符未找到，添加新的标识符到符号表
            symbol_ptr[Name] = (int64_t)ch_ptr;        // 存储标识符字符串的地址
            symbol_ptr[Hash] = current_hash_val;       // 存储哈希值
            token = symbol_ptr[Token] = Id;            // 设置为标识符类型
            return;
        }
        // 处理数字常量：以数字开头的字符序列
        else if (token >= '0' && token <= '9')
        {
            token_val = token - '0'; // 将字符转换为数字
            // 继续读取数字的剩余部分
            while (*src >= '0' && *src <= '9')
            {
                token_val = token_val * 10 + *src++ - '0'; // 构建完整的数字
            }
            token = Num; // 设置标记类型为数字
            return;
        }
        // 处理除法操作符和注释
        else if (token == '/')
        {
            if (*src == '/') // 双斜杠表示行注释
            {
                // 跳过整行注释内容
                while (*src != 0 && *src != '\n')
                    src++;
            }
            else
            {
                token = Div; // 单个斜杠是除法操作符
                return;
            }
        }
        // 处理等号相关操作符
        else if (token == '=')
        {
            if (*src == '=') // == 等于操作符
            {
                src++;
                token = Eq;
            }
            else // = 赋值操作符
            {
                token = Assign;
            }
            return;
        }
        // 处理感叹号相关操作符
        else if (token == '!')
        {
            if (*src == '=') // != 不等于操作符
            {
                src++;
                token = Ne;
            }
            else
            {
                // 单独的'!'在L25中不是有效操作符，返回ASCII值让语法分析器处理错误
            }
            return;
        }
        // 处理小于号相关操作符
        else if (token == '<')
        {
            if (*src == '=') // <= 小于等于操作符
            {
                src++;
                token = Le;
            }
            else // < 小于操作符
            {
                token = Lt;
            }
            return;
        }
        // 处理大于号相关操作符
        else if (token == '>')
        {
            if (*src == '=') // >= 大于等于操作符
            {
                src++;
                token = Ge;
            }
            else // > 大于操作符
            {
                token = Gt;
            }
            return;
        }
        // 处理算术操作符
        else if (token == '+')
        {
            token = Add; // 加法操作符
            return;
        }
        else if (token == '-')
        {
            token = Sub; // 减法操作符
            return;
        }
        else if (token == '*')
        {
            token = Mul; // 乘法操作符
            return;
        }
        // 处理结构体和数组相关的新操作符
        else if (token == '.')
        {
            token = Dot; // 点操作符，用于结构体成员访问
            return;
        }
        else if (token == ':')
        {
            token = Colon; // 冒号操作符，用于类型注解
            return;
        }
        // 处理单字符标点符号：括号、大括号、方括号、逗号、分号
        else if (token == '(' || token == ')' || token == '{' || token == '}' || token == ',' || token == ';' ||
                 token == '[' || token == ']')
        {
            return; // 直接返回ASCII值，语法分析器会识别这些字符
        }
        // 隐式跳过未知字符，继续循环处理下一个字符
    }
}

int load_src(char *file)
{
#define int int64_t // 在函数内部重新定义int为int64_t
    int fd;         // 文件描述符
    int cnt;        // 读取的字节数

    // 打开源代码文件
    if ((fd = open(file, O_RDONLY)) < 0)
    {
        printf("Error: Could not open source file '%s'\n", file);
        return -1;
    }

    // 分配源代码缓冲区
    // src用于词法分析时移动，src_dump保留原始地址用于释放内存
    if (!(src = src_dump = malloc(MAX_SIZE)))
    {
        printf("Error: Could not malloc for source code buffer\n");
        close(fd);
        return -1;
    }

    // 读取文件内容到缓冲区
    if ((cnt = read(fd, src, MAX_SIZE - 1)) <= 0)
    {
        printf("Error: Could not read source code from '%s' or file is empty\n", file);
        free(src_dump);
        src = src_dump = NULL;
        close(fd);
        return -1;
    }

    // 添加字符串结束符
    // 这很重要，将内存数据转换为标准C字符串
    // 使得后续的字符串处理函数能正确工作
    src[cnt] = 0;
    close(fd);

    return 0; // 加载成功
#undef int    // 清理局部定义
}
#define int int64_t // 恢复全局定义

#ifdef int
#undef int // 在main函数签名前取消int的重定义
#endif



int main (int argc, char *argv[]) {
    #define int int64_t // 在main函数内部重新定义int为int64_t

    MAX_SIZE = 256 * 1024 * 8;
    load_src(argv[1]);

    for (int i = 0; *src; src++, i++) {
        printf("%ld: ", i);
        printf("%c\n", *src);
    }
    // 当编译器看到变量 x 时，它可以去 symbol_table 里查一下 x 的类型是整型还是别的什么。


    return 0;
}