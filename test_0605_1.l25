program ArrayStructTest {
    struct Point {
        x;
        y;
    }
    
    func distance(p1x, p1y, p2x, p2y) {
        let dx;
        let dy;
        dx = p1x - p2x;
        dy = p1y - p2y;
        return dx * dx + dy * dy;
    }
    
    main {
        let points[3];
        let p : Point;
        let i;
        let dist;
        
        // 初始化结构体
        p.x = 10;
        p.y = 20;
        
        // 初始化数组
        points[0] = 1;
        points[1] = 2;
        points[2] = 3;
        
        // 输出结果
        output(p.x, p.y);
        
        i = 0;
        while (i < 3) {
            output(points[i]);
            i = i + 1;
        }
        
        // 计算距离
        dist = distance(p.x, p.y, points[0], points[1]);
        output(dist);
    }
}