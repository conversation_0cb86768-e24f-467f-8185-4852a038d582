program TestComplexLogic {

  func isPositive(num) {
    let result = 0;
    if (num > 0) {
      result = 1;
    }; // End of if
    return result;
  }

  func factorial(n) {
    let fact = 1;
    let i = 1;
    if (isPositive(n) == 1) { // Using function call in condition (indirectly via comparison)
      while (i <= n) {
        fact = fact * i;
        i = i + 1;
      }; // End of while
    } else {
      fact = 0; // Factorial of non-positive is 0 or undefined, let's return 0
    }; // End of if-else
    return fact;
  }

  main {
    let x = 5;
    let y = -2;
    let z = 0;

    output(factorial(x)); // Expected: 120
    output(factorial(y)); // Expected: 0
    output(factorial(z)); // Expected: 0 (or 1, depending on definition for 0!) - current logic gives 0

    let a = 3;
    let b = 4;
    output(isPositive(a - b)); // Expected: 0 (since a-b is -1)
    output(isPositive(b - a)); // Expected: 1 (since b-a is 1)
  }
}