0000: IMM   0
0002: NVAR  2
0004: LEA   -1
0006: PUSH 
0007: LEA   3
0009: LI   
0010: PUSH 
0011: LEA   3
0013: LI   
0014: MUL  
0015: SI   
0016: LEA   -2
0018: PUSH 
0019: LEA   2
0021: LI   
0022: PUSH 
0023: LEA   2
0025: LI   
0026: MUL  
0027: SI   
0028: LEA   -1
0030: LI   
0031: PUSH 
0032: LEA   -2
0034: LI   
0035: ADD  
0036: RET  
0037: NVAR  6
0039: IMM   101
0041: PTAX 
0042: LEA   -2
0044: LEAS  0
0046: PUSH 
0047: IMM   3
0049: SI   
0050: LEA   -2
0052: LEAS  1
0054: PUSH 
0055: IMM   0
0057: PUSH 
0058: IMM   4
0060: SUB  
0061: SI   
0062: LEA   -4
0064: LEAS  0
0066: PUSH 
0067: IMM   10
0069: SI   
0070: LEA   -4
0072: LEAS  1
0074: PUSH 
0075: IMM   5
0077: SI   
0078: IMM   102
0080: PTAX 
0081: LEA   -2
0083: LEAS  0
0085: LI   
0086: PTAX 
0087: LEA   -2
0089: LEAS  1
0091: LI   
0092: PTAX 
0093: LEA   -4
0095: LEAS  0
0097: LI   
0098: PTAX 
0099: LEA   -4
0101: LEAS  1
0103: LI   
0104: PTAX 
0105: LEA   -4
0107: LEAS  0
0109: LI   
0110: PUSH 
0111: LEA   -2
0113: LEAS  0
0115: LI   
0116: GT   
0117: JZ    4153832416
0119: IMM   888
0121: PTAX 
0122: LEA   -5
0124: PUSH 
0125: LEA   -2
0127: LEAS  0
0129: LI   
0130: PUSH 
0131: LEA   -2
0133: LEAS  1
0135: LI   
0136: PUSH 
0137: CALL  4153831456
0139: DARG  2
0141: SI   
0142: LEA   -5
0144: LI   
0145: PTAX 
0146: LEA   -6
0148: PUSH 
0149: LEA   -4
0151: LEAS  1
0153: LI   
0154: PUSH 
0155: LEA   -2
0157: LEAS  1
0159: LI   
0160: SUB  
0161: SI   
0162: LEA   -6
0164: LI   
0165: PTAX 
0166: IMM   0
0168: PUSH 
0169: EXIT 
