0000: IMM   0
0002: NVAR  1
0004: LEA   -1
0006: PUSH 
0007: LEA   3
0009: LI   
0010: PUSH 
0011: LEA   2
0013: LI   
0014: ADD  
0015: SI   
0016: LEA   -1
0018: LI   
0019: RET  
0020: NVAR  1
0022: LEA   -1
0024: PUSH 
0025: LEA   2
0027: LI   
0028: PUSH 
0029: LEA   2
0031: LI   
0032: MUL  
0033: SI   
0034: LEA   -1
0036: LI   
0037: RET  
0038: NVAR  3
0040: LEA   -1
0042: PUSH 
0043: IMM   5
0045: SI   
0046: LEA   -2
0048: PUSH 
0049: LEA   -1
0051: LI   
0052: PUSH 
0053: IMM   10
0055: PUSH 
0056: CALL  4153196576
0058: DARG  2
0060: SI   
0061: LEA   -3
0063: PUSH 
0064: LEA   -2
0066: LI   
0067: PUSH 
0068: CALL  4153196720
0070: DARG  1
0072: SI   
0073: LEA   -3
0075: LI   
0076: PUSH 
0077: IMM   50
0079: GT   
0080: JZ    4153197264
0082: LEA   -3
0084: LI   
0085: PTAX 
0086: JMP   4153197288
0088: IMM   0
0090: PTAX 
0091: IMM   0
0093: PUSH 
0094: EXIT 
