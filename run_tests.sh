#!/bin/bash

# L25编译器测试套件
# 验证修复和扩展功能

echo "🚀 L25编译器测试套件开始"
echo "=================================="

# 编译L25编译器
echo "📦 编译L25编译器..."
gcc -o L25 L25.c -std=c99
if [ $? -ne 0 ]; then
    echo "❌ L25编译器编译失败"
    exit 1
fi
echo "✅ L25编译器编译成功"

# 测试函数
run_test() {
    local test_name=$1
    local test_file=$2
    local expected_success=$3
    
    echo ""
    echo "🧪 测试: $test_name"
    echo "文件: $test_file"
    echo "预期: $expected_success"
    echo "---"
    
    # 运行测试
    ./L25 "$test_file" > "output_$test_name.txt" 2>&1
    local exit_code=$?
    
    if [ "$expected_success" = "success" ]; then
        if [ $exit_code -eq 0 ]; then
            echo "✅ $test_name - 通过"
            echo "输出:"
            cat "output_$test_name.txt"
        else
            echo "❌ $test_name - 失败 (退出码: $exit_code)"
            echo "错误输出:"
            cat "output_$test_name.txt"
        fi
    else
        if [ $exit_code -ne 0 ]; then
            echo "✅ $test_name - 通过 (正确捕获错误)"
            echo "错误信息:"
            head -5 "output_$test_name.txt"
        else
            echo "❌ $test_name - 失败 (应该报错但没有)"
        fi
    fi
}

# 运行所有测试
echo ""
echo "🎯 开始运行测试用例..."

# 基础语法测试
run_test "基础语法" "test_basic_syntax.l25" "success"

# 数组功能测试  
run_test "数组功能" "test_array_features.l25" "success"

# 结构体功能测试
run_test "结构体功能" "test_struct_features.l25" "success"

# 安全修复测试
run_test "安全修复" "test_security_fixes.l25" "success"

# 复杂功能测试
run_test "复杂功能" "test_complex_features.l25" "success"

# 错误处理测试
run_test "错误处理" "test_error_cases.l25" "success"

echo ""
echo "🏁 测试完成"
echo "=================================="

# 清理临时文件
echo "🧹 清理临时文件..."
rm -f output_*.txt assemble.l25

echo "✨ 测试套件执行完毕"
