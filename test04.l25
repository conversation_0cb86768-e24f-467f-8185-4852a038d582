program TestAdvancedIOAndBool {

  func compareAndPrint(valA, valB) {
    output(valA, valB); // 测试多参数 output

    if (valA == valB) {
      output(1); // 若 valA == valB 则输出 1
    } else {
      output(0); // 否则输出 0
    };

    if (valA != valB) {
      output(1); // 若 valA != valB 则输出 1
    } else {
      output(0); // 否则输出 0
    };

    if (valA < valB) {
      output(1); // 若 valA < valB 则输出 1
    } else {
      output(0); // 否则输出 0
    };

    if (valA <= valB) {
      output(1); // 若 valA <= valB 则输出 1
    } else {
      output(0); // 否则输出 0
    };

    if (valA > valB) {
      output(1); // 若 valA > valB 则输出 1
    } else {
      output(0); // 否则输出 0
    };

    if (valA >= valB) {
      output(1); // 若 valA >= valB 则输出 1
    } else {
      output(0); // 否则输出 0
    };
    return 0; // 函数必须有返回值
  }

  func getFixedValue() {
    return 77; // 测试仅有 return 的函数
  }

  main {
    let num1;
    let num2;
    let temp_return_val; // 用于接收函数的返回值

    output(1111);        // 提示输入第一组数字
    input(num1, num2);   // 测试多参数 input
    temp_return_val = compareAndPrint(num1, num2);

    let fixed_val = getFixedValue();
    output(fixed_val);   // 输出 getFixedValue 的结果

    output(2222);        // 提示输入循环上限
    input(num1);         // 重用 num1 作为循环上限

    let counter = 0;
    while (counter < num1) {
      output(counter);
      counter = counter + 1;
    }; // 分号结束 while 语句
  }
}